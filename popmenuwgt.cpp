#include "popmenuwgt.h"
#include "ui_popmenuwgt.h"

#include <QLabel>
#include <QPushButton>

PopMenuWgt::PopMenuWgt(QWidget *parent) : QWidget(parent), ui(new Ui::PopMenuWgt)
{
    ui->setupUi(this);

    this->setWindowFlags(Qt::FramelessWindowHint | Qt::Tool | Qt::WindowStaysOnTopHint);
    this->setFocusPolicy(Qt::NoFocus);
    this->setWindowModality(Qt::NonModal);
}

PopMenuWgt::~PopMenuWgt()
{
    delete ui;
}

void PopMenuWgt::SetCards(QList<CardWgt *> &cards)
{
    Clear();
    current_cards_ = cards; // 保存卡片列表

    if (cards.count() > 0)
    {
        this->setFixedHeight(36 * cards.count());
        for (int i = 0; i < cards.count(); ++i)
        {
            QPushButton *btn = new QPushButton();
            btn->setMinimumSize(204, 36);
            if (cards[i]->GetType() == 0)
            {
                btn->setIcon(QIcon(cards[i]->GetNormalIconPath()));
            }
            else
            {
                btn->setIcon(QIcon(cards[i]->GetSimpleIconPath()));
            }
            
            btn->setIconSize(QSize(20, 20));
            btn->setText(cards[i]->GetName());
            btn->setProperty("cardIndex", i); // 保存卡片索引

            // 连接按钮点击事件
            connect(btn, &QPushButton::clicked, this, &PopMenuWgt::OnCardButtonClicked);

            btn->setStyleSheet(R"(
                               QPushButton
                               {
                               font-family: Microsoft YaHei, Microsoft YaHei;
                               font-weight: 400;
                               font-size: 8px;
                               color: #4B5C77;
                               background: transparent;
                               border-radius:0px;
                               text-align:left;
                               padding-left:8px;
                               }
                               QPushButton:hover
                               {
                               background: #ECF3FF;
                               border-radius: 2px;
                               }
                               )");
            ui->verticalLayout_2->addWidget(btn);
        }
    }
    else
    {
        QLabel *label = new QLabel();
        label->setAlignment(Qt::AlignCenter);
        label->setMinimumSize(204, 26);
        label->setText("没有匹配的工具！换个词试试...");
        label->setStyleSheet(R"(
                             font-family: Microsoft YaHei, Microsoft YaHei;
                             font-weight: 400;
                             font-size: 8px;
                             color: #818999;
                             background:transparent;
                             border-radius:0px;
                             )");
        ui->verticalLayout_2->addWidget(label);
        this->setFixedHeight(26);
    }
}

void PopMenuWgt::OnCardButtonClicked()
{
    QPushButton *btn = qobject_cast<QPushButton *>(sender());
    if (btn)
    {
        int cardIndex = btn->property("cardIndex").toInt();
        if (cardIndex >= 0 && cardIndex < current_cards_.count())
        {
            int cardId = current_cards_[cardIndex]->GetCardId();
            emit cardClicked(cardId);
        }
    }
}

void PopMenuWgt::Clear()
{
    while (QLayoutItem *item = ui->verticalLayout_2->takeAt(0))
    {
        if (QWidget *widget = item->widget())
        {
            delete widget;
        }
        delete item;
    }
}
