/* 主窗口边框样式 */
QWidget#DxwsWgt
{
    border: 1px solid #E1E5E9;
    border-radius: 10px;
    background-color: #FFFFFF;
}

QWidget#wgt_navi
{
	background: #FFFFFF;
	border-radius: 4px 0px 0px 4px;
}

QWidget#wgt_r_bg
{
	border-image: url(:/img/r_bg_simple.png);
}

QWidget#wgt_navi
{
	border-image: url(:/img/navi_bg.png);
}

QPushButton#btn_wdgj
{
	border-image: url(:/img/common_simple.png);
}
QPushButton#btn_wdgj:hover
{
	
	border-image: url(:/img/common_simple_hover.png);
}
QPushButton#btn_wdgj:checked 
{
	
	border-image: url(:/img/common_simple_checked.png);
}

QPushButton#btn_ywgj
{
	border-image: url(:/img/ywgj_simple.png);
}
QPushButton#btn_ywgj:hover
{
	
	border-image: url(:/img/ywgj_simple_hover.png);
}
QPushButton#btn_ywgj:checked 
{
	
	border-image: url(:/img/ywgj_simple_checked.png);
}

QPushButton#btn_ycyc
{
	border-image: url(:/img/ycyc_simple.png);
}
QPushButton#btn_ycyc:hover
{
	
	border-image: url(:/img/ycyc_simple_hover.png);
}
QPushButton#btn_ycyc:checked 
{
	
	border-image: url(:/img/ycyc_simple_checked.png);
}

QPushButton#btn_cyrj
{
	border-image: url(:/img/cyrj_simple.png);
}
QPushButton#btn_cyrj:hover
{
	
	border-image: url(:/img/cyrj_simple_hover.png);
}
QPushButton#btn_cyrj:checked 
{
	
	border-image: url(:/img/cyrj_simple_checked.png);
}

QPushButton#btn_cpfl
{
	border-image: url(:/img/cpfl_simple.png);
}
QPushButton#btn_cpfl:hover
{
	
	border-image: url(:/img/cpfl_simple_hover.png);
}
QPushButton#btn_cpfl:checked 
{
	
	border-image: url(:/img/cpfl_simple_checked.png);
}