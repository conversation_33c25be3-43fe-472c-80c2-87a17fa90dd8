#ifndef CYRJMANAGER_H
#define CYRJMANAGER_H

#include "basemanager.h"
#include "cardids.h"
#include "cardwgt.h"
#include <QList>
#include <QObject>
#include <QWidget>

class CyrjManager : public BaseManager
{
    Q_OBJECT

  public:
    explicit CyrjManager(QWidget *parent = nullptr);
    ~CyrjManager();

    // 初始化常用软件相关功能
    void InitCyrj();

    // 添加工具卡片
    void AddCyrj(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId);

    // 获取卡片列表
    QList<CardWgt *> GetCyrjCards() const
    {
        return cyrj_cards_;
    }

    // 处理卡片点击事件
    void HandleCardClick(int cardId);

    // 处理卡片收藏事件
    void HandleCardCollect(int cardId, bool isCollected);

  private:
    // 常用软件区域功能函数
    void LaunchCyrjBaiduNetdisk();
    void LaunchCyrjOldBaidu();
    void LaunchCyrjPinyinInput();
    void LaunchCyrjDBC2000();
    void LaunchCyrjCommonBrowsers();
    void LaunchCyrj360Cloud();
    void LaunchCyrjTencentWeiyun();
    void LaunchCyrjWubiInput();
    void LaunchCyrjMSSQL();
    void LaunchCyrjCustomEnv();
    void LaunchCyrjAliDrive();

    void LaunchCyrjMSRuntime();
    void LaunchCyrjProcessExplorer();
    void LaunchCyrjShowDesktopIcons();

  private:
    QList<CardWgt *> cyrj_cards_;
};

#endif // CYRJMANAGER_H
