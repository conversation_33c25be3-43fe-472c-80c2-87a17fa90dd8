#include "dxwswgt.h"
#include "cardids.h"
#include "cpflmanager.h"
#include "ui_dxwswgt.h"

#include <QAction>
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QProcess>
#include <QPushButton>
#include <QSet>
#include <QStandardPaths>
#include <QTextStream>
#include <QThread.h>
#include <QTimer>

#include <DbgHelp.h>
#include <Psapi.h>
#include <Shlwapi.h>
#include <Windows.h>
#include <crtdbg.h>
#include <shellapi.h>
#include <signal.h>
#include <tchar.h>
#include <tlhelp32.h>
#include <winreg.h>
#pragma comment(lib, "dbghelp.lib")
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "Shlwapi.lib")

// 生成dump文件的函数

LONG WINAPI DxUnhandledExceptionFilter(EXCEPTION_POINTERS *pExceptionPointers)
{
    // 获取当前时间用于文件名
    SYSTEMTIME st;
    GetLocalTime(&st);

    // 创建dump文件名
    wchar_t dumpFileName[MAX_PATH];
    swprintf_s(dumpFileName, MAX_PATH, L"DxwsApp_Crash_%04d%02d%02d_%02d%02d%02d.dmp", st.wYear, st.wMonth, st.wDay,
               st.wHour, st.wMinute, st.wSecond);

    // 创建dump文件
    HANDLE hDumpFile = CreateFileW(dumpFileName, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);

    if (hDumpFile != INVALID_HANDLE_VALUE)
    {
        MINIDUMP_EXCEPTION_INFORMATION dumpInfo;
        dumpInfo.ThreadId = GetCurrentThreadId();
        dumpInfo.ExceptionPointers = pExceptionPointers;
        dumpInfo.ClientPointers = FALSE;

        // 设置dump包含的信息类型 - 包含尽可能多的信息
        MINIDUMP_TYPE dumpType =
            (MINIDUMP_TYPE)(MiniDumpWithDataSegs | MiniDumpWithHandleData | MiniDumpWithUnloadedModules |
                            MiniDumpWithIndirectlyReferencedMemory | MiniDumpScanMemory |
                            MiniDumpWithProcessThreadData | MiniDumpWithThreadInfo // 包含AVX扩展状态
            );

        // 写入dump文件
        BOOL result =
            MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hDumpFile, dumpType, &dumpInfo, NULL, NULL);

        CloseHandle(hDumpFile);
    }
    else
    {
        qDebug() << "无法创建转储文件，错误代码：" << GetLastError();
    }

    return EXCEPTION_EXECUTE_HANDLER;
}

// 设置异常处理器
void SetupCrashHandler()
{
    SetUnhandledExceptionFilter(DxUnhandledExceptionFilter);

    // 防止其他库覆盖我们的异常处理器
    SetErrorMode(SEM_FAILCRITICALERRORS | SEM_NOGPFAULTERRORBOX);

    // 设置无效参数处理器
    _set_invalid_parameter_handler([](const wchar_t *, const wchar_t *, const wchar_t *, unsigned int, uintptr_t) {
        RaiseException(0xE0000001, EXCEPTION_NONCONTINUABLE, 0, NULL);
    });

    // 设置纯虚函数调用处理器
    _set_purecall_handler([]() { RaiseException(0xE0000002, EXCEPTION_NONCONTINUABLE, 0, NULL); });

    // 设置abort信号处理器
    signal(SIGABRT, [](int) { RaiseException(0xE0000003, EXCEPTION_NONCONTINUABLE, 0, NULL); });

    // 设置浮点异常处理器
    signal(SIGFPE, [](int) { RaiseException(0xE0000004, EXCEPTION_NONCONTINUABLE, 0, NULL); });

    // 设置非法指令处理器
    signal(SIGILL, [](int) { RaiseException(0xE0000005, EXCEPTION_NONCONTINUABLE, 0, NULL); });

    // 设置段错误处理器
    signal(SIGSEGV, [](int) { RaiseException(0xE0000006, EXCEPTION_NONCONTINUABLE, 0, NULL); });

    // 设置终止信号处理器
    signal(SIGTERM, [](int) { RaiseException(0xE0000007, EXCEPTION_NONCONTINUABLE, 0, NULL); });
}

// 全局崩溃处理器设置函数
void SetupGlobalCrashHandler()
{
    SetUnhandledExceptionFilter(DxUnhandledExceptionFilter);

    // 防止其他库覆盖我们的异常处理器
    SetErrorMode(SEM_FAILCRITICALERRORS | SEM_NOGPFAULTERRORBOX);

    // 设置进程默认堆错误处理
    HeapSetInformation(NULL, HeapEnableTerminationOnCorruption, NULL, 0);
}

DxwsWgt::DxwsWgt(QWidget *parent) : QWidget(parent), ui(new Ui::DxwsWgt)
{
    // 设置崩溃处理器

    SetupCrashHandler();

    ui->setupUi(this);

    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground, false); // 确保背景不透明以显示边框

    // 设置窗口图标
    setWindowIcon(QIcon(":/WindowsAdapter.ico"));

    ui->wgt_title->installEventFilter(this);
    ui->wgt_search->installEventFilter(this);
    ui->lineEdit_search->installEventFilter(this);
    ui->wgt_common->installEventFilter(this);

    conf_wgt_ = new ConfWgt;
    connect(conf_wgt_, &ConfWgt::sigChangeSkin, this, &DxwsWgt::OnChangeSkin);

    // 初始化功能管理器
    wdgj_manager_ = new WdgjManager(this);
    ywgj_manager_ = new YwgjManager(this);
    ycyc_manager_ = new YcycManager(this);
    cyrj_manager_ = new CyrjManager(this);
    cpfl_manager_ = new CpflManager(this);

    app_data_manager_ = new AppDataManager(this);

    // 将AppDataManager传递给配置窗口
    conf_wgt_->setAppDataManager(app_data_manager_);

    app_data_manager_->loadData();

    // 加载配置窗口的主题偏好
    conf_wgt_->loadThemePreference();

    // 应用保存的主题
    int savedTheme = app_data_manager_->getThemePreference();
    QString themePath = (savedTheme == 0) ? ":/qss/normal.qss" : ":/qss/simple.qss";

    // 连接管理器的信号
    connect(wdgj_manager_, &WdgjManager::cardClicked, this, &DxwsWgt::OnCardClicked);
    connect(ywgj_manager_, &YwgjManager::cardClicked, this, &DxwsWgt::OnCardClicked);
    connect(ycyc_manager_, &YcycManager::cardClicked, this, &DxwsWgt::OnCardClicked);
    connect(cyrj_manager_, &CyrjManager::cardClicked, this, &DxwsWgt::OnCardClicked);

    connect(wdgj_manager_, &WdgjManager::collectClicked, this, &DxwsWgt::OnCardCollectClicked);
    connect(ywgj_manager_, &YwgjManager::collectClicked, this, &DxwsWgt::OnCardCollectClicked);
    connect(ycyc_manager_, &YcycManager::collectClicked, this, &DxwsWgt::OnCardCollectClicked);
    connect(cyrj_manager_, &CyrjManager::collectClicked, this, &DxwsWgt::OnCardCollectClicked);

    InitTray();

    // 应用保存的主题
    ApplyTheme(themePath);

    // 初始化配置文件
    InitializeConfigFiles();

    pop_menu_wgt_ = new PopMenuWgt();

    // 连接搜索结果窗口的信号
    connect(pop_menu_wgt_, &PopMenuWgt::cardClicked, this, &DxwsWgt::OnCardClicked);

    ui->btn_clear->hide();

    // 初始化各个功能模块
    wdgj_manager_->InitWdgj();
    ywgj_manager_->InitYwgj();
    ycyc_manager_->InitYcyc();
    cyrj_manager_->InitCyrj();
    ui->wgt_common->resize(503, 100);
    ui->wgt_collected->resize(503, 100);
    ui->wgt_ywgj->resize(503, 100);
    ui->wgt_ycyc->resize(503, 100);
    ui->wgt_cyrj->resize(503, 100);

    // 更新各页面卡片的收藏状态
    SyncCardCollectionStatus();

    UpdateWdgjCommon();
    UpdateWdgjCollected();
    UpdateYwgj();
    UpdateYcyc();
    UpdateCyrj();

    // // 在所有卡片初始化完成后应用保存的主题
    // OnChangeSkin(savedTheme, themePath);

    ui->btn_max->hide();

    // 更新系统信息
    UpdateSystemInfo();

    ui->wgt_yaq->resize(510, 100);
    ui->wgt_ywaq->resize(510, 100);
    ui->wgt_aqyy->resize(510, 100);
    ui->wgt_aqfw->resize(510, 100);

    cpfl_manager_->Init(ui->wgt_yaq, ui->sawc_yaq, ui->wgt_ywaq, ui->sawc_ywaq, ui->wgt_aqyy, ui->sawc_aqyy,
                        ui->wgt_aqfw, ui->sawc_aqfw);

    UpdateSystemInfo();

    // 最后确保所有卡片（包括产品福利）都应用了正确的主题
    OnChangeSkin(savedTheme, themePath);
}

DxwsWgt::~DxwsWgt()
{
    app_data_manager_->saveData();
    // 清理管理器
    delete wdgj_manager_;
    delete ywgj_manager_;
    delete ycyc_manager_;
    delete cyrj_manager_;
    delete cpfl_manager_;
    delete app_data_manager_;

    delete ui;
}

void DxwsWgt::InitTray()
{
    tray_menu_ = new QMenu(this);
    QIcon icon(":/img/logo_tray.png");
    tray_icon_ = new QSystemTrayIcon(this);
    tray_icon_->setIcon(icon);
    tray_icon_->setToolTip("德迅工具箱");
    QAction *show_win = new QAction(QIcon(":/img/show_win.png"), "显示面板窗口", this);
    connect(show_win, &QAction::triggered, this, &DxwsWgt::OnShowWin);
    QAction *conf = new QAction(QIcon(":/img/conf_btn.png"), "设置", this);
    connect(conf, &QAction::triggered, this, &DxwsWgt::OnConf);
    // QAction *restart = new QAction(QIcon(":/img/restart.png"), "重启", this);
    // connect(restart, &QAction::triggered, this, &DxwsWgt::OnRestart);
    QAction *exit = new QAction(QIcon(":/img/exit.png"), "退出", this);
    connect(exit, &QAction::triggered, this, &DxwsWgt::OnExit);

    // 设置菜单样式表
    tray_menu_->setStyleSheet(R"(
                              /* 菜单整体样式 */
                              QMenu {
                              background-color: rgba(255,255,255,0.5);      /* 背景色 */
                              border: 1px solid ##575757;      /* 边框 */
                              padding: 5px 0;                 /* 内边距 */
                              }

                              /* 菜单项样式 */
                              QMenu::item {
                              background-color: #FFFFFF;      /* 背景色 */
                              height: 32px;                   /* 行高 */
                              padding: 0 25px 0 30px;         /* 左右内边距（图标和文本位置） */
                              margin: 0 5px;                  /* 上下外边距 */
                              border: 1px solid transparent;  /* 默认无边框 */
                              }

                              /* 菜单项悬停效果 */
                              QMenu::item:selected {
                              background-color: #ECF3FF;      /* 悬停背景色 */
                              border-radius: 0px;             /* 圆角 */
                              }

                              /* 菜单项图标位置 */
                              QMenu::icon {
                              left: 8px;                      /* 图标距离左侧的距离 */
                              }

                              /* 分隔线样式 */
                              QMenu::separator {
                              height: 1px;
                              background: #cccccc;
                              margin: 4px 8px;
                              }
                              )");

    tray_menu_->addAction(show_win);
    tray_menu_->addAction(conf);
    //    tray_menu_->addAction(restart);
    tray_menu_->addAction(exit);
    tray_icon_->setContextMenu(tray_menu_);

    // 连接托盘图标双击事件
    connect(tray_icon_, &QSystemTrayIcon::activated, this, &DxwsWgt::OnTrayIconActivated);

    tray_icon_->show();

    // qDebug() << tray_menu_->styleSheet();
}

void DxwsWgt::ApplyTheme(const QString &theme)
{
    QString styleSheet;

    // 检查是哪个主题，并尝试从缓存加载
    if (theme.endsWith("normal.qss"))
    {
        if (normal_style_sheet_.isEmpty())
        {
            QFile file(theme);
            if (file.open(QFile::ReadOnly | QFile::Text))
            {
                QTextStream stream(&file);
                normal_style_sheet_ = stream.readAll();
                file.close();
            }
        }
        styleSheet = normal_style_sheet_;
    }
    else if (theme.endsWith("simple.qss"))
    {
        if (simple_style_sheet_.isEmpty())
        {
            QFile file(theme);
            if (file.open(QFile::ReadOnly | QFile::Text))
            {
                QTextStream stream(&file);
                simple_style_sheet_ = stream.readAll();
                file.close();
            }
        }
        styleSheet = simple_style_sheet_;
    }
    else
    {
        // 对于未知主题，仍然直接加载
        QFile file(theme);
        if (file.open(QFile::ReadOnly | QFile::Text))
        {
            QTextStream stream(&file);
            styleSheet = stream.readAll();
            file.close();
        }
    }

    // 应用样式表
    if (!styleSheet.isEmpty())
    {
        qApp->setStyleSheet(styleSheet);
    }
}

bool DxwsWgt::event(QEvent *event)
{
    if (event->type() == QEvent::WindowDeactivate)
    {
        // 窗口失去焦点时隐藏搜索结果窗口
        pop_menu_wgt_->hide();
    }
    return QWidget::event(event);
}

// 产品福利相关方法已迁移到CpflManager

bool DxwsWgt::eventFilter(QObject *watched, QEvent *event)
{
    // 只处理标题栏部件的事件
    if (watched == ui->wgt_title)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

        switch (event->type())
        {
        case QEvent::MouseButtonPress:
            // 鼠标左键按下
            if (mouseEvent->button() == Qt::LeftButton)
            {
                start_pos_ = mouseEvent->globalPos() - frameGeometry().topLeft();
                is_pressed_ = true;
                return true; // 事件已处理
            }
            break;

        case QEvent::MouseMove:
            // 鼠标移动且左键被按下
            if (is_pressed_ && (mouseEvent->buttons() & Qt::LeftButton))
            {
                move(mouseEvent->globalPos() - start_pos_);
                // 窗口移动时隐藏搜索结果窗口
                pop_menu_wgt_->hide();
                return true; // 事件已处理
            }
            break;

        case QEvent::MouseButtonRelease:
            // 鼠标左键释放
            if (mouseEvent->button() == Qt::LeftButton)
            {
                is_pressed_ = false;
                return true; // 事件已处理
            }
            break;

        default:
            break;
        }
    }
    else if (watched == ui->wgt_search)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
        switch (event->type())
        {
        case QMouseEvent::Enter: {
            if (is_search_input_)
            {
                break;
            }
            ui->wgt_search->setStyleSheet(R"(
                                          border: 1px solid #7A869A;
                                          border-radius: 6px;
                                          )");
        }
        break;
        case QMouseEvent::Leave: {
            if (is_search_input_)
            {
                break;
            }
            ui->wgt_search->setStyleSheet(R"(
                                          border: 1px solid #E0E0E2;
                                          border-radius: 6px;
                                          )");
        }
        break;
        }
    }
    else if (watched == ui->lineEdit_search)
    {
        switch (event->type())
        {
        case QEvent::FocusIn: {
            is_search_input_ = true;
            ui->wgt_search->setStyleSheet(R"(
                                          border: 1px solid #0665FF;
                                          border-radius: 6px;
                                          )");
        }
        break;
        case QEvent::FocusOut: {
            is_search_input_ = false;
            ui->wgt_search->setStyleSheet(R"(
                                          border: 1px solid #7A869A;
                                          border-radius: 6px;
                                          )");
        }
        break;
        }
    }
    else if (watched == ui->wgt_common)
    {
        switch (event->type())
        {
        case QEvent::Resize: {
            // 防止在布局更新过程中的递归调用
            if (!is_updating_layout_)
            {
                UpdateWdgjCommon();
            }
        }
        break;
        case QEvent::MouseButtonPress: {
            // 点击其他区域时隐藏搜索结果窗口
            pop_menu_wgt_->hide();
        }
        break;
        }
    }

    // 其他事件交给基类处理
    return QWidget::eventFilter(watched, event);
}

void DxwsWgt::resizeEvent(QResizeEvent *event)
{
    //    UpdateWdgjCommon();
    //    UpdateWdgjCollected();
    //    UpdateYwgj();
}

void DxwsWgt::UpdateWdgjCommon()
{
    // 防止递归调用
    if (is_updating_layout_)
    {
        return;
    }

    is_updating_layout_ = true;

    QLayout *layout_common = ui->wgt_common->layout();

    if (layout_common)
    {
        while (QLayoutItem *item = layout_common->takeAt(0))
        {
            if (QWidget *widget = item->widget())
            {
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout_common;
    }

    // Clear the manager's list before repopulating
    for (CardWgt *card : wdgj_manager_->GetCommonCards())
    {
        delete card;
    }
    wdgj_manager_->GetCommonCards().clear();

    QList<int> top_card_ids = app_data_manager_->getTopClickedCards();
    for (int id : top_card_ids)
    {
        CardWgt *card = GetCardById(id);
        if (card)
        {
            wdgj_manager_->AddWdgjCommon(card->GetNormalIconPath(), card->GetSimpleIconPath(), card->GetName(),
                                         card->GetCardId());
        }
    }

    QList<CardWgt *> wdgj_common_cards = wdgj_manager_->GetCommonCards();
    int col_cnt = (ui->wgt_common->width() + 14) / (155 + 14);
    qDebug() << "ui->wgt_common->width(): " << ui->wgt_common->width();
    qDebug() << "col_cnt: " << col_cnt;
    int row_cnt = wdgj_common_cards.count() / col_cnt;
    if (wdgj_common_cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(24);
    v_layout->setContentsMargins(0, 0, 0, 0);
    ui->wgt_common->setLayout(v_layout);

    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();
        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > wdgj_common_cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                h_layout->addWidget(wdgj_common_cards[card_idx], 1);
                wdgj_common_cards[card_idx]->show();
                // qDebug() << "show card";
            }
        }
    }

    // 应用当前主题到新创建的常用卡片
    int currentTheme = app_data_manager_->getThemePreference();
    QList<CardWgt *> common_cards = wdgj_manager_->GetCommonCards();
    for (CardWgt *card : common_cards)
    {
        card->SetTheme(currentTheme);
    }

    // 重置标志位
    is_updating_layout_ = false;
}

void DxwsWgt::UpdateYwgj()
{
    QLayout *layout = ui->wgt_ywgj->layout();

    if (layout)
    {
        while (QLayoutItem *item = layout->takeAt(0))
        {
            if (QWidget *widget = item->widget())
            {
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout;
    }

    QList<CardWgt *> ywgj_cards = ywgj_manager_->GetYwgjCards();

    int col_cnt = (ui->wgt_ywgj->width() + 14) / (155 + 14);
    qDebug() << "ui->wgt_ywgj->width(): " << ui->wgt_ywgj->width();
    int row_cnt = ywgj_cards.count() / col_cnt;
    if (ywgj_cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    int delta_row = row_cnt - 5;
    if (delta_row > 0)
    {
        ui->sawc_ywgj->resize(562, 400 + delta_row * (54 + 24));
    }

    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(24);
    v_layout->setContentsMargins(0, 0, 0, 0);
    ui->wgt_ywgj->setLayout(v_layout);
    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();
        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > ywgj_cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                h_layout->addWidget(ywgj_cards[card_idx], 1);
                ywgj_cards[card_idx]->show();
            }
        }
    }
}

void DxwsWgt::UpdateYcyc()
{
    QLayout *layout = ui->wgt_ycyc->layout();

    if (layout)
    {
        while (QLayoutItem *item = layout->takeAt(0))
        {
            if (QWidget *widget = item->widget())
            {
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout;
    }

    QList<CardWgt *> ycyc_cards = ycyc_manager_->GetYcycCards();

    int col_cnt = (ui->wgt_ycyc->width() + 14) / (155 + 14);

    int row_cnt = ycyc_cards.count() / col_cnt;
    if (ycyc_cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    int delta_row = row_cnt - 5;
    if (delta_row > 0)
    {
        ui->sawc_ycyc->resize(562, 400 + delta_row * (54 + 24));
    }
    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(24);
    v_layout->setContentsMargins(0, 0, 0, 0);
    ui->wgt_ycyc->setLayout(v_layout);
    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();

        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > ycyc_cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                h_layout->addWidget(ycyc_cards[card_idx], 1);
                ycyc_cards[card_idx]->show();
                qDebug() << "show card";
            }
        }
    }
}

void DxwsWgt::UpdateCyrj()
{
    QLayout *layout = ui->wgt_cyrj->layout();

    if (layout)
    {
        while (QLayoutItem *item = layout->takeAt(0))
        {
            if (QWidget *widget = item->widget())
            {
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout;
    }

    QList<CardWgt *> cyrj_cards = cyrj_manager_->GetCyrjCards();

    int col_cnt = (ui->wgt_cyrj->width() + 14) / (155 + 14);

    int row_cnt = cyrj_cards.count() / col_cnt;
    if (cyrj_cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    int delta_row = row_cnt - 5;
    if (delta_row > 0)
    {
        ui->sawc_cyrj->resize(562, 400 + delta_row * (54 + 24));
    }

    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(24);
    v_layout->setContentsMargins(0, 0, 0, 0);
    ui->wgt_cyrj->setLayout(v_layout);
    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();
        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > cyrj_cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                h_layout->addWidget(cyrj_cards[card_idx], 1);
                cyrj_cards[card_idx]->show();
                // qDebug() << "show card";
            }
        }
    }
}

// 产品福利Update方法已迁移到CpflManager，现在通过槽函数调用

void DxwsWgt::UpdateWdgjCollected()
{
    // 防止递归调用
    if (is_updating_collected_layout_)
    {
        return;
    }

    is_updating_collected_layout_ = true;

    QLayout *layout = ui->wgt_collected->layout();

    if (layout)
    {
        while (QLayoutItem *item = layout->takeAt(0))
        {
            if (QWidget *widget = item->widget())
            {
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout;
    }

    // Clear the manager's list before repopulating
    qDeleteAll(wdgj_manager_->GetCollectedCards());
    wdgj_manager_->GetCollectedCards().clear();

    QList<int> favorite_ids = app_data_manager_->getFavoriteLayout();
    for (int id : favorite_ids)
    {
        CardWgt *card = GetCardById(id);
        if (card)
        {
            wdgj_manager_->AddWdgjCollected(card->GetNormalIconPath(), card->GetSimpleIconPath(), card->GetName(),
                                            card->GetCardId());
        }
    }

    QList<CardWgt *> wdgj_collected_cards = wdgj_manager_->GetCollectedCards();
    int col_cnt = (ui->wgt_collected->width() + 14) / (155 + 14);
    qDebug() << "ui->wgt_collected->width(): " << ui->wgt_collected->width();
    int row_cnt = wdgj_collected_cards.count() / col_cnt;
    if (wdgj_collected_cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    int delta_row = row_cnt - 3;
    if (delta_row > 0)
    {
        ui->sawc_wdgj->resize(562, 400 + delta_row * (54 + 24));
    }

    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(24);
    v_layout->setContentsMargins(0, 0, 0, 0);
    ui->wgt_collected->setLayout(v_layout);
    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();
        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > wdgj_collected_cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                h_layout->addWidget(wdgj_collected_cards[card_idx], 1);
                wdgj_collected_cards[card_idx]->show();
                // qDebug() << "show card";
            }
        }
    }

    // 应用当前主题到新创建的收藏卡片
    int currentTheme = app_data_manager_->getThemePreference();
    QList<CardWgt *> collected_cards = wdgj_manager_->GetCollectedCards();
    for (CardWgt *card : collected_cards)
    {
        card->SetTheme(currentTheme);
    }

    // 重置标志位
    is_updating_collected_layout_ = false;
}

void DxwsWgt::OnShowWin()
{
    // 显示窗口并置顶
    if (this->isMinimized())
    {
        this->showNormal();
    }
    else if (!this->isVisible())
    {
        this->show();
    }

    // 置顶并激活窗口
    this->raise();
    this->activateWindow();

    // 确保窗口在前台显示
    this->setWindowState((this->windowState() & ~Qt::WindowMinimized) | Qt::WindowActive);
}

void DxwsWgt::OnConf()
{
    conf_wgt_->show();
}

void DxwsWgt::OnRestart()
{
}

void DxwsWgt::OnExit()
{
    qApp->quit();
}

void DxwsWgt::on_btn_conf_clicked()
{
    // 每次打开配置窗口时重新加载主题偏好
    conf_wgt_->loadThemePreference();
    conf_wgt_->show();
}

void DxwsWgt::on_btn_min_clicked()
{
    this->showMinimized();
}

void DxwsWgt::on_btn_max_clicked()
{
    if (this->isMaximized())
    {
        this->showNormal();
    }
    else
    {
        this->showMaximized();
    }
}

void DxwsWgt::on_btn_close_clicked()
{
    // 隐藏到托盘而不是关闭程序
    this->hide();

    // 首次隐藏到托盘时显示提示
    static bool firstHide = true;
    if (firstHide && tray_icon_ && tray_icon_->isVisible())
    {
        tray_icon_->showMessage("程序已最小化到托盘", "程序仍在后台运行，双击托盘图标可重新显示窗口",
                                QSystemTrayIcon::Information, 3000);
        firstHide = false;
    }
}

void DxwsWgt::OnChangeSkin(int type, QString path)
{
    // 让UI先更新显示"正在切换"状态
    QApplication::processEvents();

    // 添加短暂延迟以确保"正在切换"状态能被看到
    QTimer::singleShot(200, [this, type, path]() {
        // 执行主题切换操作
        ApplyTheme(path);

        // 保存主题偏好
        if (app_data_manager_)
        {
            app_data_manager_->setThemePreference(type);
        }

        // 优化：使用QSet避免重复处理卡片
        QSet<CardWgt *> unique_cards_to_update;

        // 添加所有卡片到集合中，自动去重
        for (CardWgt *card : wdgj_manager_->GetCommonCards())
        {
            if (card)
                unique_cards_to_update.insert(card);
        }
        for (CardWgt *card : wdgj_manager_->GetCollectedCards())
        {
            if (card)
                unique_cards_to_update.insert(card);
        }
        for (CardWgt *card : ywgj_manager_->GetYwgjCards())
        {
            if (card)
                unique_cards_to_update.insert(card);
        }
        for (CardWgt *card : ycyc_manager_->GetYcycCards())
        {
            if (card)
                unique_cards_to_update.insert(card);
        }
        for (CardWgt *card : cyrj_manager_->GetCyrjCards())
        {
            if (card)
                unique_cards_to_update.insert(card);
        }

        // 批量更新主题，每个卡片只处理一次
        for (CardWgt *card : unique_cards_to_update)
        {
            card->SetTheme(type);
        }

        // 单独处理CpflCardWgt（使用QSet确保唯一性）
        QSet<CpflCardWgt *> unique_cpfl_cards;
        for (CpflCardWgt *card : cpfl_manager_->GetCpflCards())
        {
            if (card)
                unique_cpfl_cards.insert(card);
        }

        for (CpflCardWgt *card : unique_cpfl_cards)
        {
            card->SetTheme(type);
        }

        // 通知配置窗口主题切换完成
        if (conf_wgt_)
        {
            conf_wgt_->onThemeSwitchCompleted();
        }
    });
}

void DxwsWgt::on_btn_wdgj_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_wdgj);
}

void DxwsWgt::on_btn_ywgj_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_ywgj);
}

void DxwsWgt::on_btn_ycyc_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_ycyc);
}

void DxwsWgt::on_btn_cyrj_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_cyrj);
}

void DxwsWgt::on_btn_cpfl_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_cpfl);
    QApplication::processEvents();
    if (cpfl_manager_)
    {
        cpfl_manager_->UpdateYaq(ui->wgt_yaq, ui->sawc_yaq);
        cpfl_manager_->UpdateYwaq(ui->wgt_ywaq, ui->sawc_ywaq);
        cpfl_manager_->UpdateAqyy(ui->wgt_aqyy, ui->sawc_aqyy);
        cpfl_manager_->UpdateAqfw(ui->wgt_aqfw, ui->sawc_aqfw);
    }
}

void DxwsWgt::on_btn_clear_clicked()
{
    ui->lineEdit_search->clear();
    ui->btn_clear->hide();
    // 清除搜索内容时隐藏搜索结果窗口
    pop_menu_wgt_->hide();
}

void DxwsWgt::on_btn_search_clicked()
{
}

void DxwsWgt::on_lineEdit_search_textChanged(const QString &arg1)
{
    ui->btn_clear->show();

    SearchFilter(arg1);
}

void DxwsWgt::on_lineEdit_search_editingFinished()
{
    QString s = ui->lineEdit_search->text();
    if (s.isEmpty())
    {
        ui->btn_clear->hide();
    }
    else
    {
        ui->btn_clear->show();
    }
}

void DxwsWgt::SearchFilter(const QString &s)
{
    if (s.isEmpty())
    {
        // 搜索字符串为空时隐藏搜索结果窗口
        pop_menu_wgt_->hide();
        return;
    }
    QList<CardWgt *> cards;
    QSet<int> addedCardIds; // 用于跟踪已添加的卡片ID

    // 从所有管理器中搜索匹配的卡片
    QList<CardWgt *> all_cards;
    all_cards.append(wdgj_manager_->GetCommonCards());
    all_cards.append(wdgj_manager_->GetCollectedCards());
    all_cards.append(ywgj_manager_->GetYwgjCards());
    all_cards.append(ycyc_manager_->GetYcycCards());
    all_cards.append(cyrj_manager_->GetCyrjCards());

    for (CardWgt *card : all_cards)
    {
        QString name = card->GetName();
        // 改为大小写不敏感的搜索
        if (name.contains(s, Qt::CaseInsensitive))
        {
            if (!addedCardIds.contains(card->GetCardId()))
            {
                cards.push_back(card);
                addedCardIds.insert(card->GetCardId());
            }
        }
    }

    pop_menu_wgt_->SetCards(cards);

    QPoint pos = ui->wgt_title->mapToGlobal(ui->wgt_search->pos());
    pop_menu_wgt_->move(pos.x() + 8, pos.y() + ui->wgt_search->height() + 2);
    pop_menu_wgt_->show();
}

void DxwsWgt::paintEvent(QPaintEvent *event)
{
    // 调用父类的paintEvent实现默认绘制
    QWidget::paintEvent(event);
}

#ifdef Q_OS_WIN
bool DxwsWgt::nativeEvent(const QByteArray &eventType, void *message, long *result)
{
    if (eventType == "windows_generic_MSG")
    {
        MSG *msg = static_cast<MSG *>(message);

        // 处理时间同步线程的回调消息
        if (msg->message == WM_USER + 1001)
        {
            bool success = (msg->wParam == 1);

            if (success)
            {
                QMessageBox::information(this, "时间同步", "时间同步命令执行成功。");
                qDebug() << "时间同步命令执行成功";
            }
            else
            {
                QMessageBox::warning(this, "时间同步", "时间同步失败，请检查网络连接或以管理员权限运行。");
                qDebug() << "时间同步命令执行失败";
            }

            *result = 0;
            return true;
        }
        // 处理重启剪贴板线程的回调消息
        else if (msg->message == WM_USER + 1002)
        {
            bool success = (msg->wParam == 1);

            if (success)
            {
                QMessageBox::information(this, "成功", "剪贴板服务已重启。");
                qDebug() << "剪贴板服务重启成功";
            }
            else
            {
                QMessageBox::warning(this, "错误", "无法重启剪贴板服务。");
                qDebug() << "剪贴板服务重启失败";
            }

            *result = 0;
            return true;
        }
    }

    return QWidget::nativeEvent(eventType, message, result);
}
#endif

void DxwsWgt::OnCardClicked(int cardId)
{
    app_data_manager_->incrementClickCount(cardId);
    UpdateWdgjCommon();

    // 处理卡片点击事件
    qDebug() << "卡片被点击, ID: " << cardId;

    // 根据卡片ID范围委托给相应的管理器
 
    if (cardId >= CARD_ID_RANGE_YWGJ_START && cardId <= CARD_ID_RANGE_YWGJ_END)
    {
        // 运维工具区域 (2001-2099)
        ywgj_manager_->HandleCardClick(cardId);
    }
    else if (cardId >= CARD_ID_RANGE_YCYC_START && cardId <= CARD_ID_RANGE_YCYC_END)
    {
        // 压测远程区域 (3001-3099)
        ycyc_manager_->HandleCardClick(cardId);
    }
    else if (cardId >= CARD_ID_RANGE_CYRJ_START && cardId <= CARD_ID_RANGE_CYRJ_END)
    {
        // 常用软件区域 (4001-4099)
        cyrj_manager_->HandleCardClick(cardId);
    }
    else
    {
        qDebug() << "未知卡片ID: " << cardId;
        // 通用的启动逻辑 - 显示提示信息
        QMessageBox::information(this, "应用启动", QString("您点击了卡片ID: %1\n功能正在开发中...").arg(cardId));
    }

    // 点击后隐藏搜索结果窗口
    pop_menu_wgt_->hide();
}

void DxwsWgt::on_btn_aqfw_clicked()
{
    ui->stackedWidget_2->setCurrentWidget(ui->page_aqfw);
    if (cpfl_manager_)
    {
        cpfl_manager_->UpdateAqfw(ui->wgt_aqfw, ui->sawc_aqfw);
    }
}

void DxwsWgt::InitializeConfigFiles()
{
    // 检查配置文件是否存在，如果不存在则释放内嵌的配置文件
    if (!CheckConfigFilesExist())
    {
        qDebug() << "配置文件不存在，开始释放内嵌文件...";
        ExtractConfigFiles();
        qDebug() << "配置文件释放完成";
    }
    else
    {
        qDebug() << "配置文件已存在，跳过释放";
    }
}

bool DxwsWgt::CheckConfigFilesExist()
{
    // 获取应用程序所在目录
    QString appDir = QApplication::applicationDirPath();
    QString configDir = appDir + "/config";

    // 检查主config目录是否存在
    QDir dir(configDir);
    if (!dir.exists())
    {
        return false;
    }

    // 检查关键文件是否存在
    QStringList keyFiles = {"config.ini", "7z.exe", "7z.dll", "axel.exe", "cygwin1.dll"};

    for (const QString &file : keyFiles)
    {
        if (!QFile::exists(configDir + "/" + file))
        {
            return false;
        }
    }

    // 检查子目录是否存在
    QStringList subDirs = {"sub/software", "sub/ycyc", "sub/ywgj", "RDPWrap"};
    for (const QString &subDir : subDirs)
    {
        if (!QDir(configDir + "/" + subDir).exists())
        {
            return false;
        }
    }

    return true;
}

void DxwsWgt::ExtractConfigFiles()
{
    QString appDir = QApplication::applicationDirPath();
    QString configDir = appDir + "/config";

    // 创建config目录及子目录
    QDir().mkpath(configDir);
    QDir().mkpath(configDir + "/sub/software");
    QDir().mkpath(configDir + "/sub/ycyc");
    QDir().mkpath(configDir + "/sub/ywgj");
    QDir().mkpath(configDir + "/RDPWrap");

    // 释放主config文件
    QStringList mainFiles = {"7z.dll", "7z.exe", "axel.exe", "config.ini", "cygwin1.dll"};

    for (const QString &file : mainFiles)
    {
        ExtractResourceFile(":/config/" + file, configDir + "/" + file);
    }

    // 释放software子目录文件
    QStringList softwareFiles = {"360CloudDisk.bat",     "AliCloudDisk.bat",      "BaiduNetdisk.bat",
                                 "CommonBrowsers.bat",   "CustomEnvironment.bat", "dbc2000.bat",
                                 "FastGuard.bat",        "MicrosoftRuntime.bat",  "mssql.bat",
                                 "OldBaiduDisk.bat",     "PinyinInputIME.bat",    "ProcessExplorer.bat",
                                 "ShowDesktopIcons.bat", "TencentWeiyun.bat",     "WubiInputIME.bat"};

    for (const QString &file : softwareFiles)
    {
        ExtractResourceFile(":/config/sub/software/" + file, configDir + "/sub/software/" + file);
    }

    // 释放ycyc子目录文件
    QStringList ycycFiles = {
        "AIDA64.bat",        "Cinebench.bat", "CPUZ.bat",    "Diskmark.bat",
        "Geekbench.bat",     "LuDashi.bat",   "Prime95.bat", "RestartRemoteService.bat",
        "SystemMonitor.exe",
    };

    for (const QString &file : ycycFiles)
    {
        ExtractResourceFile(":/config/sub/ycyc/" + file, configDir + "/sub/ycyc/" + file);
    }

    // 释放ywgj子目录文件
    QStringList ywgjFiles = {
        "IISFullInstall.bat",
        "IISDefaultInstall.bat",
        "kms.bat",
        "net35.bat",
        "net4x.bat",
        "PartitionAssistant.bat",
        "VirtualizationDetection.bat",
        "WindowsOptimizedImages.bat",

    };

    for (const QString &file : ywgjFiles)
    {
        ExtractResourceFile(":/config/sub/ywgj/" + file, configDir + "/sub/ywgj/" + file);
    }

    // 释放RDPWrap目录文件
    QStringList rdpWrapFiles = {"install.bat",  "RDPCheck.exe",  "RDPConf.exe",
                                "RDPWInst.exe", "uninstall.bat", "update.bat"};

    for (const QString &file : rdpWrapFiles)
    {
        ExtractResourceFile(":/config/RDPWrap/" + file, configDir + "/RDPWrap/" + file);
    }
}

void DxwsWgt::ExtractResourceFile(const QString &resourcePath, const QString &targetPath)
{
    QFile resourceFile(resourcePath);
    if (!resourceFile.open(QIODevice::ReadOnly))
    {
        qDebug() << "无法打开资源文件:" << resourcePath;
        return;
    }

    QFile targetFile(targetPath);
    if (!targetFile.open(QIODevice::WriteOnly))
    {
        qDebug() << "无法创建目标文件:" << targetPath;
        resourceFile.close();
        return;
    }

    // 复制文件内容
    targetFile.write(resourceFile.readAll());

    resourceFile.close();
    targetFile.close();

    qDebug() << "释放文件成功:" << targetPath;
}

void DxwsWgt::OnCardCollectClicked(int cardId, bool isCollected)
{
    // Find the source card from any manager to update its state
    CardWgt *source_card = GetCardById(cardId);

    if (source_card)
    {
        source_card->SetCollected(isCollected);
    }

    QList<int> favorite_ids = app_data_manager_->getFavoriteLayout();
    if (isCollected)
    {
        if (!favorite_ids.contains(cardId))
        {
            favorite_ids.append(cardId);
        }
    }
    else
    {
        favorite_ids.removeAll(cardId);
    }
    app_data_manager_->setFavoriteLayout(favorite_ids);

    // 更新收藏夹视图
    UpdateWdgjCollected();
}

CardWgt *DxwsWgt::GetCardById(int cardId)
{
    // This function should find the "master" card from the original lists, not the copies.
    QList<CardWgt *> all_master_cards;
    all_master_cards.append(wdgj_manager_->GetCommonCards()); // Assuming these are the masters now
    all_master_cards.append(ywgj_manager_->GetYwgjCards());
    all_master_cards.append(ycyc_manager_->GetYcycCards());
    all_master_cards.append(cyrj_manager_->GetCyrjCards());

    for (CardWgt *card : all_master_cards)
    {
        if (card->GetCardId() == cardId)
        {
            return card;
        }
    }

    // If not found, it might be a collected card that's not in the master lists anymore
    // This part of the logic might need refinement depending on how "common" cards are defined.
    // For now, we also check the collected list.
    for (CardWgt *card : wdgj_manager_->GetCollectedCards())
    {
        if (card->GetCardId() == cardId)
        {
            return card;
        }
    }

    return nullptr;
}

void DxwsWgt::OnTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason)
    {
    case QSystemTrayIcon::DoubleClick:
        // 双击托盘图标时显示主窗口
        OnShowWin();
        break;
    case QSystemTrayIcon::Trigger:
        // 单击托盘图标时的处理（可选）
        // 暂时不做处理，避免与右键菜单冲突
        break;
    default:
        break;
    }
}

void DxwsWgt::UpdateSystemInfo()
{
    QString systemInfo;

    // 获取CPU信息
    QString cpuInfo = "Unknown CPU";
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", 0, KEY_READ, &hKey) ==
        ERROR_SUCCESS)
    {
        wchar_t buffer[256];
        DWORD bufferSize = sizeof(buffer);
        if (RegQueryValueEx(hKey, L"ProcessorNameString", NULL, NULL, (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS)
        {
            cpuInfo = QString::fromWCharArray(buffer).trimmed();
        }
        RegCloseKey(hKey);
    }

    // 获取内存信息
    QString memoryInfo = "Unknown Memory";
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus))
    {
        // 将字节转换为GB，四舍五入
        double totalMemoryGB = static_cast<double>(memStatus.ullTotalPhys) / (1024.0 * 1024.0 * 1024.0);
        memoryInfo = QString::number(static_cast<int>(totalMemoryGB + 0.5)) + "GB";
    }

    // 组合系统信息
    systemInfo = QString("CPU型号:%1 当前系统内存:%2").arg(cpuInfo, memoryInfo);

    // 更新UI标签
    ui->label_2->setText(systemInfo);
}

void DxwsWgt::SyncCardCollectionStatus()
{
    // 获取收藏卡片列表
    QList<int> favorite_ids = app_data_manager_->getFavoriteLayout();

    // 更新"我的工具"模块中所有卡片的收藏状态
    QList<CardWgt *> wdgj_cards = wdgj_manager_->GetCommonCards();
    for (CardWgt *card : wdgj_cards)
    {
        bool is_collected = favorite_ids.contains(card->GetCardId());
        card->SetCollected(is_collected);
    }

    // 更新"运维工具"模块中所有卡片的收藏状态
    QList<CardWgt *> ywgj_cards = ywgj_manager_->GetYwgjCards();
    for (CardWgt *card : ywgj_cards)
    {
        bool is_collected = favorite_ids.contains(card->GetCardId());
        card->SetCollected(is_collected);
    }

    // 更新"压测远程"模块中所有卡片的收藏状态
    QList<CardWgt *> ycyc_cards = ycyc_manager_->GetYcycCards();
    for (CardWgt *card : ycyc_cards)
    {
        bool is_collected = favorite_ids.contains(card->GetCardId());
        card->SetCollected(is_collected);
    }

    // 更新"常用软件"模块中所有卡片的收藏状态
    QList<CardWgt *> cyrj_cards = cyrj_manager_->GetCyrjCards();
    for (CardWgt *card : cyrj_cards)
    {
        bool is_collected = favorite_ids.contains(card->GetCardId());
        card->SetCollected(is_collected);
    }

    qDebug() << "所有卡片的收藏状态已同步";
}

void DxwsWgt::on_btn_aqyy_clicked()
{
    ui->stackedWidget_2->setCurrentWidget(ui->page_aqyy);
    if (cpfl_manager_)
    {
        cpfl_manager_->UpdateAqyy(ui->wgt_aqyy, ui->sawc_aqyy);
    }
}

void DxwsWgt::on_btn_yaq_clicked()
{
    ui->stackedWidget_2->setCurrentWidget(ui->page_yaq);
    if (cpfl_manager_)
    {
        cpfl_manager_->UpdateYaq(ui->wgt_yaq, ui->sawc_yaq);
    }
}

void DxwsWgt::on_btn_ywaq_clicked()
{
    ui->stackedWidget_2->setCurrentWidget(ui->page_ywaq);
    if (cpfl_manager_)
    {
        cpfl_manager_->UpdateYwaq(ui->wgt_ywaq, ui->sawc_ywaq);
    }
}
