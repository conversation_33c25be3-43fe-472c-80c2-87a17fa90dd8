#include "appdatamanager.h"
#include <QCoreApplication>
#include <QDebug>
#include <algorithm>

AppDataManager::AppDataManager(QObject *parent)
    : QObject(parent), settings_(QCoreApplication::applicationDirPath() + "/appdata.ini", QSettings::IniFormat, parent)
{
}

void AppDataManager::loadData()
{
    settings_.beginGroup("CardClicks");
    QStringList keys = settings_.childKeys();
    for (const QString &key : keys)
    {
        bool ok;
        int cardId = key.toInt(&ok);
        if (ok)
        {
            cardClickCounts_[cardId] = settings_.value(key).toInt();
        }
    }
    settings_.endGroup();

    settings_.beginGroup("Favorites");
    int size = settings_.beginReadArray("layout");
    for (int i = 0; i < size; ++i)
    {
        settings_.setArrayIndex(i);
        favoriteLayout_.append(settings_.value("cardId").toInt());
    }
    settings_.endArray();
    settings_.endGroup();

    qDebug() << "Loaded card click counts:" << cardClickCounts_;
    qDebug() << "Loaded favorite layout:" << favoriteLayout_;

    // 加载主题偏好 (0: 正常版, 1: 简约版)
    int themePreference = settings_.value("Settings/theme", 0).toInt();
    qDebug() << "Loaded theme preference:" << themePreference;
}

void AppDataManager::saveData()
{
    settings_.beginGroup("CardClicks");
    for (auto it = cardClickCounts_.constBegin(); it != cardClickCounts_.constEnd(); ++it)
    {
        settings_.setValue(QString::number(it.key()), it.value());
    }
    settings_.endGroup();

    settings_.beginGroup("Favorites");
    settings_.beginWriteArray("layout");
    for (int i = 0; i < favoriteLayout_.size(); ++i)
    {
        settings_.setArrayIndex(i);
        settings_.setValue("cardId", favoriteLayout_[i]);
    }
    settings_.endArray();
    settings_.endGroup();

    qDebug() << "Saved card click counts:" << cardClickCounts_;
    qDebug() << "Saved favorite layout:" << favoriteLayout_;
}

// 主题偏好设置
void AppDataManager::setThemePreference(int themeType)
{
    settings_.setValue("Settings/theme", themeType);
    qDebug() << "Saved theme preference:" << themeType;
}

int AppDataManager::getThemePreference() const
{
    return settings_.value("Settings/theme", 0).toInt();
}

void AppDataManager::incrementClickCount(int cardId)
{
    cardClickCounts_[cardId]++;
}

QList<int> AppDataManager::getTopClickedCards(int count) const
{
    QList<QPair<int, int>> sortedCounts;
    for (auto it = cardClickCounts_.constBegin(); it != cardClickCounts_.constEnd(); ++it)
    {
        sortedCounts.append(qMakePair(it.key(), it.value()));
    }
    std::partial_sort(sortedCounts.begin(), sortedCounts.begin() + std::min(count, sortedCounts.size()),
                      sortedCounts.end(), compareCardCounts);
    QList<int> topCards;
    for (int i = 0; i < std::min(count, sortedCounts.size()); ++i)
    {
        topCards.append(sortedCounts[i].first);
    }
    return topCards;
}

void AppDataManager::setFavoriteLayout(const QList<int> &layout)
{
    favoriteLayout_ = layout;
}

QList<int> AppDataManager::getFavoriteLayout() const
{
    return favoriteLayout_;
}
