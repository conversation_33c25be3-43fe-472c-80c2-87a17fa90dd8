#include "ycycmanager.h"
#include "cardids.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QProcess>
#include <QStandardPaths>

YcycManager::YcycManager(QWidget *parent) : BaseManager(parent)
{
}

YcycManager::~YcycManager()
{
    // 清理卡片
    for (CardWgt *card : ycyc_cards_)
    {
        delete card;
    }
}

void YcycManager::InitYcyc()
{
    // 自动替换后的代码
    AddYcyc(":/img/Standard/img_ycyc/ic_multiinterfaceinstallation.png",
            ":/img/Lite/img_ycyc/ic_multiinterfaceinstallation_2.png", "多界面安装", CARD_ID_MULTI_INSTALL);
    AddYcyc(":/img/Standard/img_ycyc/ic_multi interface settings.png",
            ":/img/Lite/img_ycyc/ic_multi interface settings_2.png", "多界面配置", CARD_ID_MULTI_CONFIG);
    AddYcyc(":/img/Standard/img_ycyc/ic_multiinterface uninstallation.png",
            ":/img/Lite/img_ycyc/ic_multiinterface uninstallation_2.png", "多界面卸载", CARD_ID_MULTI_UNINSTALL);

    AddYcyc(":/img/Standard/img_ycyc/ic_modifyremoteport.png", ":/img/Lite/img_ycyc/ic_modifyremoteport_2.png",
            "修改远程端口", CARD_ID_REMOTE_PORT);
    AddYcyc(":/img/Standard/img_ycyc/ic_prime95.png", ":/img/Lite/img_ycyc/ic_prime95_2.png", "Prime95",
            CARD_ID_PRIME95);
    AddYcyc(":/img/Standard/img_ycyc/ic_masterlu.png", ":/img/Lite/img_ycyc/ic_masterlu_2.png", "鲁大师",
            CARD_ID_LUDASHI);
    AddYcyc(":/img/Standard/img_ycyc/ic_geekbench.png", ":/img/Lite/img_ycyc/ic_geekbench_2.png", "Geekbench",
            CARD_ID_GEEKBENCH);

    AddYcyc(":/img/Standard/img_ycyc/ic_restartremoteservice.png", ":/img/Lite/img_ycyc/ic_restartremoteservice_2.png",
            "重启远程服务", CARD_ID_RESTART_REMOTE_SERVICE);
    AddYcyc(":/img/Standard/img_ycyc/ic_aida64.png", ":/img/Lite/img_ycyc/ic_aida64_2.png", "Aida64", CARD_ID_AIDA64);
    AddYcyc(":/img/Standard/img_ycyc/ic_systemmonitor.png", ":/img/Lite/img_ycyc/ic_systemmonitor_2.png", "系统监控",
            CARD_ID_SYSTEM_MONITOR);
    AddYcyc(":/img/Standard/img_ycyc/ic_cinebench.png", ":/img/Lite/img_ycyc/ic_cinebench_2.png", "CineBench",
            CARD_ID_CINEBENCH);

    AddYcyc(":/img/Standard/img_ycyc/ic_cpu_z.png", ":/img/Lite/img_ycyc/ic_cpu_z_2.png", "CPU-Z", CARD_ID_CPUZ);

    AddYcyc(":/img/Standard/img_ycyc/ic_diskmark.png", ":/img/Lite/img_ycyc/ic_diskmark_2.png", "DiskMark",
            CARD_ID_DISKMARK);
}

void YcycManager::AddYcyc(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId)
{
    CardWgt *card = new CardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetTheme(0);
    card->SetCardId(cardId);

    // 连接卡片点击信号
    connect(card, &CardWgt::cardClicked, this, &YcycManager::cardClicked);
    connect(card, &CardWgt::collectClicked, this, &YcycManager::collectClicked);

    ycyc_cards_.push_back(card);
}

void YcycManager::HandleCardClick(int cardId)
{
    switch (cardId)
    {
    // 压测远程区域
    case CARD_ID_MULTI_INSTALL:
        LaunchMultiInstall();
        break;
    case CARD_ID_MULTI_CONFIG:
        LaunchMultiConfig();
        break;
    case CARD_ID_MULTI_UNINSTALL:
        LaunchMultiUninstall();
        break;
    case CARD_ID_REMOTE_PORT:
        LaunchRemotePort();
        break;
    case CARD_ID_PRIME95:
        LaunchPrime95();
        break;
    case CARD_ID_LUDASHI:
        LaunchLudashi();
        break;
    case CARD_ID_GEEKBENCH:
        LaunchGeekbench();
        break;
    case CARD_ID_CINEBENCH:
        LaunchCinebench();
        break;
    case CARD_ID_DISKMARK:
        LaunchDiskmark();
        break;
    case CARD_ID_RESTART_REMOTE_SERVICE:
        LaunchRestartRemoteService();
        break;
    case CARD_ID_AIDA64:
        LaunchAida64();
        break;
    case CARD_ID_SYSTEM_MONITOR:
        LaunchSystemMonitor();
        break;

    case CARD_ID_CPUZ:
        LaunchCPUZ();
        break;

    }
}

void YcycManager::LaunchMultiInstall()
{
    ExecuteConfigFile("config/RDPWrap/Install.bat", "多界面安装");
    // 提示 重启生效
    QMessageBox::information(parent_widget_, "提示", "多界面安装完成，请重启计算机以使更改生效。");
}

void YcycManager::LaunchMultiConfig()
{
    ExecuteConfigFile("config/RDPWrap/RDPConf.exe", "多界面配置");
}

void YcycManager::LaunchMultiUninstall()
{
    ExecuteConfigFile("config/RDPWrap/Uninstall.bat", "多界面卸载");
    // 提示 重启生效
    QMessageBox::information(parent_widget_, "提示", "多界面卸载完成，请重启计算机以使更改生效。");
}

void YcycManager::LaunchRemotePort()
{
    ExecuteConfigFile("config/RDPWrap/RDPConf.exe", "修改远程端口");
}

void YcycManager::LaunchPrime95()
{
    ExecuteConfigFile("config/sub/ycyc/Prime95.bat", "Prime95");
}

void YcycManager::LaunchLudashi()
{
    ExecuteConfigFile("config/sub/ycyc/Ludashi.bat", "鲁大师");
}

void YcycManager::LaunchGeekbench()
{
    ExecuteConfigFile("config/sub/ycyc/Geekbench.bat", "Geekbench");
}

void YcycManager::LaunchCinebench()
{
    ExecuteConfigFile("config/sub/ycyc/Cinebench.bat", "CineBench");
}

void YcycManager::LaunchDiskmark()
{
    ExecuteConfigFile("config/sub/ycyc/Diskmark.bat", "DiskMark");
}

void YcycManager::LaunchRestartRemoteService()
{
    ExecuteConfigFile("config/sub/ycyc/RestartRemoteService.bat", "重启远程服务");
}

void YcycManager::LaunchAida64()
{
    ExecuteConfigFile("config/sub/ycyc/Aida64.bat", "Aida64");
}

void YcycManager::LaunchSystemMonitor()
{
    ExecuteConfigFile("config/sub/ycyc/SystemMonitor.exe", "系统监控");
}

void YcycManager::LaunchCPUZ()
{
    ExecuteConfigFile("config/sub/ycyc/CPUZ.bat", "CPU-Z");
}

void YcycManager::HandleCardCollect(int cardId, bool isCollected)
{
    // The actual logic is handled in DxwsWgt::OnCardCollectClicked
    // This function is here to fulfill the virtual function requirement if we make it pure virtual in the future.
    Q_UNUSED(cardId);
    Q_UNUSED(isCollected);
}
