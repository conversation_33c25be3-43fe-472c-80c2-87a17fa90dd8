{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.26100.0", "compilerPath": "cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "compileCommands": ["${workspaceFolder}/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Release/.qtc_clangd/compile_commands.json"]}], "version": 4}