#include "wdgjmanager.h"
#include "cardids.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QProcess>
#include <QStandardPaths>

WdgjManager::WdgjManager(QWidget *parent) : BaseManager(parent)
{
}

WdgjManager::~WdgjManager()
{
    // 清理卡片
    for (CardWgt *card : wdgj_common_cards_)
    {
        delete card;
    }
    for (CardWgt *card : wdgj_collected_cards_)
    {
        delete card;
    }
}

void WdgjManager::InitWdgj()
{
    // 初始不包含任何卡片
}

void WdgjManager::AddWdgjCommon(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId)
{
    CardWgt *card = new CardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetTheme(0);
    card->SetCardId(cardId);

    // 连接卡片点击信号
    connect(card, &CardWgt::cardClicked, this, &WdgjManager::cardClicked);
    connect(card, &CardWgt::collectClicked, this, &WdgjManager::collectClicked);

    wdgj_common_cards_.push_back(card);
}

void WdgjManager::AddWdgjCollected(const QString &normal_icon, const QString &simple_icon, const QString &name,
                                   int cardId)
{
    CardWgt *card = new CardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetTheme(0);
    card->SetCardId(cardId);

    // 连接卡片点击信号
    connect(card, &CardWgt::cardClicked, this, &WdgjManager::cardClicked);
    connect(card, &CardWgt::collectClicked, this, &WdgjManager::collectClicked);

    card->SetCollected(true);
    wdgj_collected_cards_.push_back(card);
}


void WdgjManager::HandleCardCollect(int cardId, bool isCollected)
{
    if (isCollected)
    {
        // This logic is now handled by DxwsWgt which will call AddCardToCollected
    }
    else
    {
        RemoveCardFromCollected(cardId);
    }
}

void WdgjManager::AddCardToCollected(CardWgt *card)
{
    if (!card)
        return;

    // 检查是否已在收藏夹中
    for (CardWgt *collected_card : wdgj_collected_cards_)
    {
        if (collected_card->GetCardId() == card->GetCardId())
        {
            return; // 已经存在
        }
    }

    CardWgt *new_card = new CardWgt;
    new_card->setNormal_icon_path(card->GetNormalIconPath());
    new_card->setSimple_icon_path(card->GetSimpleIconPath());
    new_card->SetName(card->GetName());
    new_card->SetTheme(0);
    new_card->SetCardId(card->GetCardId());
    connect(new_card, &CardWgt::cardClicked, this, &WdgjManager::cardClicked);
    connect(new_card, &CardWgt::collectClicked, this, &WdgjManager::collectClicked);
    new_card->SetCollected(true);
    wdgj_collected_cards_.push_back(new_card);
}

void WdgjManager::RemoveCardFromCollected(int cardId)
{
    for (int i = 0; i < wdgj_collected_cards_.size(); ++i)
    {
        if (wdgj_collected_cards_[i]->GetCardId() == cardId)
        {
            CardWgt *card_to_remove = wdgj_collected_cards_.takeAt(i);
            delete card_to_remove;
            break;
        }
    }
}
