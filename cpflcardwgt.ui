<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CpflCardWgt</class>
 <widget class="QWidget" name="CpflCardWgt">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>249</width>
    <height>152</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>249</width>
    <height>152</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>249</width>
    <height>152</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background:transparent;</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true">background: #FFFFFF;
border-radius: 8px;
border: 1px solid #F1F6FF;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <widget class="QLabel" name="label_icon">
      <property name="geometry">
       <rect>
        <x>16</x>
        <y>16</y>
        <width>28</width>
        <height>28</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border:none;
background:transparent;</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLabel" name="label_name">
      <property name="geometry">
       <rect>
        <x>52</x>
        <y>21</y>
        <width>181</width>
        <height>17</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">font-family: Microsoft YaHei;
font-weight: 500;
font-size: 12px;
color: #111111;
background:transparent;
border:none;</string>
      </property>
      <property name="text">
       <string>TextLabel</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_content">
      <property name="geometry">
       <rect>
        <x>16</x>
        <y>52</y>
        <width>217</width>
        <height>91</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">font-family: Microsoft YaHei;
font-size: 12px;
color: #67768E;
background:transparent;
border:none;
</string>
      </property>
      <property name="text">
       <string>分布式节点接入， 适合任何TCP端类应用包括（游戏、APP、微端、端类内嵌Web等）。节点间切换过程中用户无感知，保持TCP连接不中断</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
      </property>
      <property name="wordWrap">
       <bool>true</bool>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
