#ifndef DXWSWGT_H
#define DXWSWGT_H

#include <QMenu>
#include <QMessageBox>
#include <QSystemTrayIcon>
#include <QWidget>

#include "appdatamanager.h"
#include "cardwgt.h"
#include "confwgt.h"
#include "cpflmanager.h"
#include "cyrjmanager.h"
#include "popmenuwgt.h"
#include "wdgjmanager.h"
#include "ycycmanager.h"
#include "ywgjmanager.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <Windows.h>
#pragma comment(lib, "user32.lib")

// 全局崩溃处理函数声明
extern void SetupGlobalCrashHandler();

QT_BEGIN_NAMESPACE
namespace Ui
{
class DxwsWgt;
}
QT_END_NAMESPACE

class DxwsWgt : public QWidget
{
    Q_OBJECT

  public:
    DxwsWgt(QWidget *parent = nullptr);
    ~DxwsWgt();

    void InitTray();

    void ApplyTheme(const QString &theme);

    // Config文件管理
    void InitializeConfigFiles();
    bool CheckConfigFilesExist();
    void ExtractConfigFiles();
    void ExtractResourceFile(const QString &resourcePath, const QString &targetPath);

    // 系统信息相关
    void UpdateSystemInfo();

    // 同步卡片收藏状态
    void SyncCardCollectionStatus();

  protected:
    bool eventFilter(QObject *watched, QEvent *event) override;
    bool event(QEvent *event) override;

    void resizeEvent(QResizeEvent *event) override;

    void paintEvent(QPaintEvent *event) override;

#ifdef Q_OS_WIN
    // Windows消息处理
    bool nativeEvent(const QByteArray &eventType, void *message, long *result) override;
#endif

  private:
    CardWgt *GetCardById(int cardId);
    void UpdateWdgjCommon();

    void UpdateWdgjCollected();

    void UpdateYwgj();

    void UpdateYcyc();

    void UpdateCyrj();

    // 产品福利相关方法现在由CpflManager处理

    void AddYaq(QString normal_icon, QString simple_icon, QString name, QString content);
    void AddYwaq(QString normal_icon, QString simple_icon, QString name, QString content);
    void AddAqyy(QString normal_icon, QString simple_icon, QString name, QString content);
    void AddAqfw(QString normal_icon, QString simple_icon, QString name, QString content);

  private slots:
    void OnShowWin();
    void OnConf();
    void OnRestart();
    void OnExit();
    void OnTrayIconActivated(QSystemTrayIcon::ActivationReason reason);

    void on_btn_conf_clicked();

    void on_btn_min_clicked();

    void on_btn_max_clicked();

    void on_btn_close_clicked();

    void OnChangeSkin(int type, QString path);

    void on_btn_wdgj_clicked();

    void on_btn_ywgj_clicked();

    void on_btn_ycyc_clicked();

    void on_btn_cyrj_clicked();

    void on_btn_cpfl_clicked();

    void on_btn_clear_clicked();

    void on_btn_search_clicked();

    void on_lineEdit_search_textChanged(const QString &arg1);

    void on_lineEdit_search_editingFinished();

    void SearchFilter(const QString &s);

    void OnCardClicked(int cardId);

    void OnCardCollectClicked(int cardId, bool isCollected);

    void on_btn_aqyy_clicked();

    void on_btn_yaq_clicked();

    void on_btn_ywaq_clicked();

    void on_btn_aqfw_clicked();

  private:
    Ui::DxwsWgt *ui;

    QSystemTrayIcon *tray_icon_ = nullptr;
    QMenu *tray_menu_ = nullptr;

    ConfWgt *conf_wgt_ = nullptr;

    // 功能管理器
    WdgjManager *wdgj_manager_ = nullptr;
    YwgjManager *ywgj_manager_ = nullptr;
    YcycManager *ycyc_manager_ = nullptr;
    CyrjManager *cyrj_manager_ = nullptr;
    CpflManager *cpfl_manager_ = nullptr;

    QPoint start_pos_;        // 记录鼠标按下时的位置
    bool is_pressed_ = false; // 标记是否正在拖动

    bool is_search_input_ = false;
    bool is_updating_layout_ = false;           // 防止递归调用UpdateWdgjCommon
    bool is_updating_collected_layout_ = false; // 防止递归调用UpdateWdgjCollected

    PopMenuWgt *pop_menu_wgt_ = nullptr;

    AppDataManager *app_data_manager_ = nullptr;

    // 缓存样式表内容
    QString normal_style_sheet_;
    QString simple_style_sheet_;

    // 崩溃测试函数（仅用于测试dump功能）
    void TestCrash();
};
#endif // DXWSWGT_H
