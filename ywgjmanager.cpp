#include "ywgjmanager.h"
#include "Shlwapi.h"
#include "cardids.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QProcess>
#include <QStandardPaths>
#include <Windows.h>
#include <qthread.h>
#include <shellapi.h>
#include <tlhelp32.h>
// 时间同步线程参数结构体
struct TimeSyncThreadParam
{
    HWND hwnd;
};

// 重启剪贴板线程参数结构体
struct RestartClipboardThreadParam
{
    HWND hwnd;
};

// 函数：检查并启动 W32Time 服务
bool EnsureW32TimeServiceIsRunning()
{
    SC_HANDLE hSCManager = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (hSCManager == NULL)
    {
        qDebug() << "无法打开服务控制管理器, 错误码:" << GetLastError();
        return false;
    }

    // 打开 W32Time 服务
    SC_HANDLE hService = OpenService(hSCManager, L"W32Time", SERVICE_QUERY_STATUS | SERVICE_START);
    if (hService == NULL)
    {
        qDebug() << "无法打开 W32Time 服务, 错误码:" << GetLastError();
        CloseServiceHandle(hSCManager);
        return false;
    }

    bool bServiceIsRunning = false;
    SERVICE_STATUS_PROCESS ssp;
    DWORD dwBytesNeeded;

    // 查询服务当前状态
    if (QueryServiceStatusEx(hService, SC_STATUS_PROCESS_INFO, (LPBYTE)&ssp, sizeof(ssp), &dwBytesNeeded))
    {
        if (ssp.dwCurrentState == SERVICE_RUNNING)
        {
            qDebug() << "W32Time 服务已在运行。";
            bServiceIsRunning = true;
        }
        else if (ssp.dwCurrentState == SERVICE_STOPPED)
        {
            qDebug() << "W32Time 服务已停止，正在尝试启动...";
            if (StartService(hService, 0, NULL))
            {
                // 等待服务启动完成，设置一个超时（例如10秒）
                for (int i = 0; i < 20; ++i)
                {
                    Sleep(500); // 等待500毫秒
                    if (QueryServiceStatusEx(hService, SC_STATUS_PROCESS_INFO, (LPBYTE)&ssp, sizeof(ssp),
                                             &dwBytesNeeded))
                    {
                        if (ssp.dwCurrentState == SERVICE_RUNNING)
                        {
                            qDebug() << "W32Time 服务启动成功。";
                            bServiceIsRunning = true;
                            break;
                        }
                    }
                }
                if (!bServiceIsRunning)
                {
                    qDebug() << "启动 W32Time 服务超时。";
                }
            }
            else
            {
                qDebug() << "启动 W32Time 服务失败, 错误码:" << GetLastError();
            }
        }
    }
    else
    {
        qDebug() << "查询 W32Time 服务状态失败, 错误码:" << GetLastError();
    }

    // 关闭句柄
    CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);

    return bServiceIsRunning;
}

DWORD WINAPI TimeSyncThreadProc(LPVOID lpParam)
{
    TimeSyncThreadParam *param = static_cast<TimeSyncThreadParam *>(lpParam);
    bool success = false;

    // 步骤 1: 确保 W32Time 服务正在运行
    if (!EnsureW32TimeServiceIsRunning())
    {
        qDebug() << "未能确保 W32Time 服务正在运行，同步中止。";
        if (param)
            PostMessage(param->hwnd, WM_USER + 1001, 0, 0); // 发送失败消息
        delete param;
        return 1; // 返回错误代码
    }

    // 服务已在运行，继续执行时间同步命令

    // 步骤 2: 构建 w32tm.exe 的完整路径
    WCHAR systemPath[MAX_PATH];
    if (GetSystemDirectoryW(systemPath, MAX_PATH) == 0)
    {
        qDebug() << "无法获取系统目录, 错误码:" << GetLastError();
        if (param)
            PostMessage(param->hwnd, WM_USER + 1001, 0, 0);
        delete param;
        return 1;
    }

    WCHAR w32tmPath[MAX_PATH];
    wcscpy_s(w32tmPath, MAX_PATH, systemPath);
    PathAppendW(w32tmPath, L"w32tm.exe");

    // 步骤 3: 准备并执行命令
    WCHAR commandLine[MAX_PATH];
    swprintf_s(commandLine, MAX_PATH, L"\"%s\" /resync", w32tmPath);

    STARTUPINFOW si = {sizeof(si)};
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE; // 隐藏命令行窗口
    PROCESS_INFORMATION pi;

    if (CreateProcessW(NULL, commandLine, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi))
    {
        qDebug() << "w32tm.exe 进程已启动，等待其完成...";

        // 等待进程执行完成 (例如，最多等待30秒)
        WaitForSingleObject(pi.hProcess, 30000);

        // 必须在使用后关闭句柄
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        success = true;
        qDebug() << "w32tm.exe /resync 命令已执行。";
    }
    else
    {
        DWORD error = GetLastError();
        qDebug() << "启动 w32tm.exe 失败, 错误码:" << error;
    }

    // 向主线程发送消息通知结果
    if (param)
    {
        PostMessage(param->hwnd, WM_USER + 1001, success ? 1 : 0, 0);
        delete param;
    }

    return success ? 0 : 1;
}

// 重启剪贴板线程函数
DWORD WINAPI RestartClipboardThreadProc(LPVOID lpParam)
{
    RestartClipboardThreadParam *param = static_cast<RestartClipboardThreadParam *>(lpParam);

    bool success = false;

    // 强制终止rdpclip.exe进程
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE)
    {
        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);

        if (Process32FirstW(hSnapshot, &pe32))
        {
            do
            {
                if (wcscmp(pe32.szExeFile, L"rdpclip.exe") == 0)
                {
                    HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, pe32.th32ProcessID);
                    if (hProcess != NULL)
                    {
                        TerminateProcess(hProcess, 0);
                        CloseHandle(hProcess);
                    }
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
    }

    // 等待进程完全终止
    Sleep(2000);

    // 启动rdpclip.exe
    STARTUPINFOW si = {sizeof(si)};
    PROCESS_INFORMATION pi;

    // 1. 获取 System32 目录的路径
    WCHAR systemPath[MAX_PATH];
    if (GetSystemDirectoryW(systemPath, MAX_PATH) == 0)
    {
        DWORD error = GetLastError();
        qDebug() << "Failed to get system directory, error code:" << error;
        return false;
    }

    // 2. 将 "rdpclip.exe" 附加到路径后面，构建完整路径
    //    例如: "C:\Windows\System32" + "\" + "rdpclip.exe"
    WCHAR rdpclipPath[MAX_PATH];
    wcscpy_s(rdpclipPath, systemPath);        // 复制基本路径
    PathAppendW(rdpclipPath, L"rdpclip.exe"); // 附加文件名

    // 3. 使用完整路径调用 CreateProcessW
    //    注意：第一个参数（lpApplicationName）现在是完整路径
    if (CreateProcessW(rdpclipPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi))
    {
        CloseHandle(pi.hThread);
        CloseHandle(pi.hProcess);
        success = true;
        qDebug() << "Successfully restarted rdpclip.exe.";
    }
    else
    {
        DWORD error = GetLastError();
        qDebug() << "Failed to restart rdpclip.exe, error code:" << error;
        // std::cerr << "Failed to restart rdpclip.exe, error code: " << error << std::endl;
    }

    // 向主线程发送消息通知结果
    PostMessage(param->hwnd, WM_USER + 1002, success ? 1 : 0, 0);

    // 释放参数内存
    delete param;

    return 0;
}

YwgjManager::YwgjManager(QWidget *parent) : BaseManager(parent)
{
}

YwgjManager::~YwgjManager()
{
    // 清理卡片
    for (CardWgt *card : ywgj_cards_)
    {
        delete card;
    }
}

void YwgjManager::InitYwgj()
{
    AddYwgj(":/img/Standard/img_ywgj/ic_kms.png", ":/img/Lite/img_ywgj/ic_kms_2.png", "KMS激活", CARD_ID_KMS_ACTIVATE);
    AddYwgj(":/img/Standard/img_ywgj/ic_winoptimization.png", ":/img/Lite/img_ywgj/ic_winoptimization_2.png", "Win优化",
            CARD_ID_WIN_OPTIMIZE);

    AddYwgj(":/img/Standard/img_ywgj/ic_net3.5.png", ":/img/Lite/img_ywgj/ic_net3.5_2.png", ".NET3.5", CARD_ID_NET35);
    AddYwgj(":/img/Standard/img_ywgj/ic_timesynchronization.png", ":/img/Lite/img_ywgj/ic_timesynchronization_2.png",
            "时间同步", CARD_ID_TIME_SYNC);
    AddYwgj(":/img/Standard/img_ywgj/ic_iisdefault.png", ":/img/Lite/img_ywgj/ic_iisdefault_2.png", "IIS默认",
            CARD_ID_IIS_DEFAULT);
    AddYwgj(":/img/Standard/img_ywgj/ic_restarttheexplorer.png", ":/img/Lite/img_ywgj/ic_restarttheexplorer_2.png",
            "重启资源器", CARD_ID_RESTART_EXPLORER);
    AddYwgj(":/img/Standard/img_ywgj/ic_partitionassistant.png", ":/img/Lite/img_ywgj/ic_partitionassistant_2.png",
            "分区助手", CARD_ID_PARTITION_ASSIST);
    AddYwgj(":/img/Standard/img_ywgj/ic_net4.x.png", ":/img/Lite/img_ywgj/ic_net4.x_2.png", ".NET4.X", CARD_ID_NET4X);
    AddYwgj(":/img/Standard/img_ywgj/ic_programsandfunctions.png", ":/img/Lite/img_ywgj/ic_programsandfunctions_2.png",
            "程序和功能", CARD_ID_ProgramAndFeatures);
    AddYwgj(":/img/Standard/img_ywgj/ic_iiscomplete.png", ":/img/Lite/img_ywgj/ic_iiscomplete_2.png", "IIS完整",
            CARD_ID_IIS_FULL);
    AddYwgj(":/img/Standard/img_ywgj/ic_restarttheadhesiveboard.png",
            ":/img/Lite/img_ywgj/ic_restarttheadhesiveboard_2.png", "重启粘贴板", CARD_ID_RESTART_CLIPBOARD);
    AddYwgj(":/img/Standard/img_ywgj/ic_virtualizationdetection.png",
            ":/img/Lite/img_ywgj/ic_virtualizationdetection_2.png", "虚拟化检测", CARD_ID_VIRTUALIZATION_CHECK);
    AddYwgj(":/img/Standard/img_ywgj/ic_poweroptions.png", ":/img/Lite/img_ywgj/ic_poweroptions_2.png", "电源选项",
            CARD_ID_POWER_OPTIONS);
    AddYwgj(":/img/Standard/img_ywgj/ic_restartthecomputer.png", ":/img/Lite/img_ywgj/ic_restartthecomputer_2.png",
            "重启计算机", CARD_ID_RESTART_COMPUTER);
}

void YwgjManager::AddYwgj(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId)
{
    CardWgt *card = new CardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetTheme(0);
    card->SetCardId(cardId);

    // 连接卡片点击信号
    connect(card, &CardWgt::cardClicked, this, &YwgjManager::cardClicked);
    connect(card, &CardWgt::collectClicked, this, &YwgjManager::collectClicked);

    ywgj_cards_.push_back(card);
}

void YwgjManager::HandleCardClick(int cardId)
{
    switch (cardId)
    {
    // 运维工具区域
    case CARD_ID_KMS_ACTIVATE:
        LaunchKMSActivate();
        break;
    case CARD_ID_WIN_OPTIMIZE:
        LaunchWinOptimize();
        break;
    case CARD_ID_NET35:
        LaunchNET35();
        break;
    case CARD_ID_TIME_SYNC:
        LaunchTimeSync();
        break;
    case CARD_ID_IIS_DEFAULT:
        LaunchIISDefault();
        break;
    case CARD_ID_RESTART_EXPLORER:
        LaunchRestartExplorer();
        break;
    case CARD_ID_PARTITION_ASSIST:
        LaunchPartitionAssist();
        break;
    case CARD_ID_NET4X:
        LaunchNET4X();
        break;
    case CARD_ID_ProgramAndFeatures:
        LaunchProgramAndFeatures();
        break;
    case CARD_ID_IIS_FULL:
        LaunchIISFull();
        break;
    case CARD_ID_RESTART_CLIPBOARD:
        LaunchRestartClipboard();
        break;
    case CARD_ID_VIRTUALIZATION_CHECK:
        LaunchVirtualCheck();
        break;
    case CARD_ID_POWER_OPTIONS:
        LaunchPowerOptions();
        break;
    case CARD_ID_RESTART_COMPUTER:
        LaunchRestartComputer();
        break;
    }
}

void YwgjManager::LaunchKMSActivate()
{
    ExecuteConfigFile("config/sub/ywgj/kms.bat", "KMS激活");
}

void YwgjManager::LaunchWinOptimize()
{
    ExecuteConfigFile("config/sub/ywgj/WindowsOptimizedImages.bat", "Win优化");
}

void YwgjManager::LaunchNET35()
{
    ExecuteConfigFile("config/sub/ywgj/net35.bat", "NET3.5");
}

void YwgjManager::LaunchTimeSync()
{
    qDebug() << "同步时间";

    // 立即显示操作开始的提示
    QMessageBox::information(parent_widget_, "时间同步", "时间同步服务已开始，请稍候...");

    // 创建线程参数
    TimeSyncThreadParam *param = new TimeSyncThreadParam;
    param->hwnd = (HWND)parent_widget_->winId(); // 获取窗口句柄

    // 使用Windows API创建线程
    HANDLE hThread = CreateThread(NULL,               // 默认安全属性
                                  0,                  // 默认堆栈大小
                                  TimeSyncThreadProc, // 线程函数
                                  param,              // 线程参数
                                  0,                  // 立即启动线程
                                  NULL                // 不需要线程ID
    );

    if (hThread == NULL)
    {
        // 线程创建失败
        delete param;
        QMessageBox::warning(parent_widget_, "错误", "无法创建时间同步线程");
        return;
    }

    // 关闭线程句柄（线程仍然运行）
    CloseHandle(hThread);
}

void YwgjManager::LaunchIISDefault()
{
    ExecuteConfigFile("config/sub/ywgj/IISDefaultInstall.bat", "IIS默认");
}

void YwgjManager::LaunchRestartExplorer()
{
    QProcess taskkillProcess;
    taskkillProcess.start("taskkill", QStringList() << "/f" << "/IM" << "explorer.exe");
    if (!taskkillProcess.waitForFinished())
    {
        // 处理错误，如果 taskkill 无法完成
        QMessageBox::warning(parent_widget_, "错误", "无法终止 explorer.exe 进程，请检查权限或手动终止。");
        return;
    }
    // 调用后可能需要等待一段时间，确保资源管理器重启完成
    QThread::sleep(2); // 等待2秒
    // 重新加载资源管理器
    // 调用 ShellExecute重新加载资源管理器

    char windowsDir[MAX_PATH] = {0};
    GetWindowsDirectoryA(windowsDir, MAX_PATH);

    HINSTANCE result = ShellExecuteA(NULL,           // 父窗口句柄，如果没有则为 NULL
                                     "open",         // 操作，"open" 表示打开
                                     "explorer.exe", // 要执行的文件或程序
                                     NULL,           // 传递给 explorer.exe 的参数，即要打开的路径
                                     windowsDir,     // 工作目录，NULL 表示使用当前目录
                                     SW_SHOWNORMAL   // 显示命令，SW_SHOWNORMAL 表示以正常方式显示窗口
    );

    qDebug() << "资源管理器重启完成";
}

void YwgjManager::LaunchPartitionAssist()
{
    ExecuteConfigFile("config/sub/ywgj/PartitionAssistant.bat", "分区助手");
}

void YwgjManager::LaunchNET4X()
{
    ExecuteConfigFile("config/sub/ywgj/net4x.bat", "NET4.X");
}

void YwgjManager::LaunchProgramAndFeatures()
{
    // 执行 appwiz.cpl 来打开“程序和功能”窗口
    // 使用 Windows API ShellExecute来执行 cmd.exe /c

    ShellExecuteA(NULL, "open", "cmd.exe", "/c appwiz.cpl", NULL, SW_SHOWNORMAL);
}

void YwgjManager::LaunchIISFull()
{
    ExecuteConfigFile("config/sub/ywgj/IISFullInstall.bat", "IIS完整");
}

void YwgjManager::LaunchRestartClipboard()
{
    // 立即显示操作开始的提示
    QMessageBox::information(parent_widget_, "重启剪贴板", "重启剪贴板服务已开始，请稍候...");

    // 创建线程参数
    RestartClipboardThreadParam *param = new RestartClipboardThreadParam;
    param->hwnd = (HWND)parent_widget_->winId(); // 获取窗口句柄

    // 使用Windows API创建线程
    HANDLE hThread = CreateThread(NULL,                       // 默认安全属性
                                  0,                          // 默认堆栈大小
                                  RestartClipboardThreadProc, // 线程函数
                                  param,                      // 线程参数
                                  0,                          // 立即启动线程
                                  NULL                        // 不需要线程ID
    );

    if (hThread == NULL)
    {
        // 线程创建失败
        delete param;
        QMessageBox::warning(parent_widget_, "错误", "无法创建剪贴板重启线程");
        return;
    }

    // 关闭线程句柄（线程仍然运行）
    CloseHandle(hThread);
}

void YwgjManager::LaunchVirtualCheck()
{
    ExecuteConfigFile("config/sub/ywgj/VirtualizationDetection.bat", "虚拟化检测");
}

void YwgjManager::LaunchPowerOptions()
{
    // 打开电源选项控制面板
    ShellExecuteA(NULL, "runas", "powercfg.cpl", NULL, NULL, SW_SHOWNORMAL);
}

void YwgjManager::LaunchRestartComputer()
{
    // 询问用户是否确认重启计算机
    int result =
        QMessageBox::question(parent_widget_, "重启计算机", "确认要重启计算机吗？", QMessageBox::Yes | QMessageBox::No);
    if (result == QMessageBox::Yes)
    {
        // 执行重启命令
        ShellExecuteA(NULL, "open", "shutdown", "/r /t 0", NULL, SW_HIDE);
    }
}

void YwgjManager::HandleCardCollect(int cardId, bool isCollected)
{
    // The actual logic is handled in DxwsWgt::OnCardCollectClicked
    // This function is here to fulfill the virtual function requirement if we make it pure virtual in the future.
    Q_UNUSED(cardId);
    Q_UNUSED(isCollected);
}
