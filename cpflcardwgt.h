#ifndef CPFLCARDWGT_H
#define CPFLCARDWGT_H

#include "cardids.h"
#include <QWidget>

namespace Ui
{
class CpflCardWgt;
}

class CpflCardWgt : public QWidget
{
    Q_OBJECT

  public:
    explicit CpflCardWgt(QWidget *parent = nullptr);
    ~CpflCardWgt();

    void setNormal_icon_path(const QString &normal_icon_path);

    void setSimple_icon_path(const QString &simple_icon_path);

    void SetTheme(int type);

    void SetName(QString name);

    void SetContent(QString content);

    void SetCardID(int cardID);

    int GetCardID() const;

  signals:
    void clicked();

  protected:
    void mousePressEvent(QMouseEvent *event) override;

  private:
    void SetIcon(QString path);

  private:
    Ui::CpflCardWgt *ui;

    QString normal_icon_path_;
    QString simple_icon_path_;

    int type_ = 0;
    int card_id_ = CARD_ID_UNKNOWN;
};

#endif // CPFLCARDWGT_H
