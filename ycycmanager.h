#ifndef YCYCMANAGER_H
#define YCYCMANAGER_H

#include "basemanager.h"
#include "cardids.h"
#include "cardwgt.h"
#include <QList>
#include <QObject>
#include <QWidget>

class YcycManager : public BaseManager
{
    Q_OBJECT

  public:
    explicit YcycManager(QWidget *parent = nullptr);
    ~YcycManager();

    // 初始化压测远程相关功能
    void InitYcyc();

    // 添加工具卡片
    void AddYcyc(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId);

    // 获取卡片列表
    QList<CardWgt *> GetYcycCards() const
    {
        return ycyc_cards_;
    }

    // 处理卡片点击事件
    void HandleCardClick(int cardId);

    // 处理卡片收藏事件
    void HandleCardCollect(int cardId, bool isCollected);

  private:
    // 压测远程区域功能函数
    void LaunchMultiInstall();
    void LaunchMultiConfig();
    void LaunchMultiUninstall();
    void LaunchRemotePort();
    void LaunchPrime95();
    void LaunchLudashi();
    void LaunchGeekbench();
    void LaunchCinebench();
    void LaunchDiskmark();
    void LaunchRestartRemoteService();
    void LaunchAida64();
    void LaunchSystemMonitor();

    void LaunchCPUZ();

  private:
    QList<CardWgt *> ycyc_cards_;
};

#endif // YCYCMANAGER_H
