#include "confwgt.h"
#include "appdatamanager.h"
#include "ui_confwgt.h"

#include <QLabel>
#include <QMouseEvent>
#include <QTimer>

ConfWgt::ConfWgt(QWidget *parent) : QWidget(parent), ui(new Ui::ConfWgt)
{
    ui->setupUi(this);

    setWindowFlags(Qt::FramelessWindowHint);

    ui->wgt_confi_title->installEventFilter(this);

    // 初始化状态提示标签
    status_label_ = new QLabel(this);
    status_label_->setStyleSheet(R"(
        QLabel {
            background-color: rgba(6, 101, 255, 0.9);
            color: white;
            border-radius: 4px;
            padding: 8px 16px;
            font-family: Microsoft YaHei;
            font-size: 12px;
        }
    )");
    status_label_->setAlignment(Qt::AlignCenter);
    status_label_->hide();

    // 初始化状态计时器
    status_timer_ = new QTimer(this);
    status_timer_->setSingleShot(true);
    connect(status_timer_, &QTimer::timeout, this, &ConfWgt::showSwitchCompleteStatus);
}

ConfWgt::~ConfWgt()
{
    delete ui;
}

bool ConfWgt::eventFilter(QObject *watched, QEvent *event)
{
    // 只处理标题栏部件的事件
    if (watched == ui->wgt_confi_title)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

        switch (event->type())
        {
        case QEvent::MouseButtonPress:
            // 鼠标左键按下
            if (mouseEvent->button() == Qt::LeftButton)
            {
                start_pos_ = mouseEvent->globalPos() - frameGeometry().topLeft();
                is_pressed_ = true;
                return true; // 事件已处理
            }
            break;

        case QEvent::MouseMove:
            // 鼠标移动且左键被按下
            if (is_pressed_ && (mouseEvent->buttons() & Qt::LeftButton))
            {
                move(mouseEvent->globalPos() - start_pos_);
                return true; // 事件已处理
            }
            break;

        case QEvent::MouseButtonRelease:
            // 鼠标左键释放
            if (mouseEvent->button() == Qt::LeftButton)
            {
                is_pressed_ = false;
                return true; // 事件已处理
            }
            break;

        default:
            break;
        }
    }

    // 其他事件交给基类处理
    return QWidget::eventFilter(watched, event);
}

void ConfWgt::on_btn_theme_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_theme);
}

void ConfWgt::on_btn_about_clicked()
{
    ui->stackedWidget->setCurrentWidget(ui->page_about);
}

void ConfWgt::on_btn_normal_clicked()
{
    ui->pic_normal->setStyleSheet(R"(
                                  border-image: url(:/img/theme_normal_checked.png);
                                  )");

    ui->pic_simple->setStyleSheet(R"(
                                  border-image: url(:/img/theme_simple.png);
                                  )");
}

void ConfWgt::on_btn_simple_clicked()
{
    ui->pic_normal->setStyleSheet(R"(
                                  border-image: url(:/img/theme_normal.png);
                                  )");

    ui->pic_simple->setStyleSheet(R"(
                                  border-image: url(:/img/theme_simple_checked.png);
                                  )");
}

void ConfWgt::on_btn_confirm_clicked()
{
    // 显示"正在切换"提示
    showSwitchingStatus();

    // 获取选择的主题类型
    int themeType = ui->btn_normal->isChecked() ? 0 : 1;
    QString themePath = (themeType == 0) ? ":/qss/normal.qss" : ":/qss/simple.qss";

    // 保存主题偏好
    if (app_data_manager_)
    {
        app_data_manager_->setThemePreference(themeType);
    }

    // 发送信号切换主题
    emit sigChangeSkin(themeType, themePath);
}

void ConfWgt::on_btn_close_clicked()
{
    this->close();
}

void ConfWgt::setAppDataManager(AppDataManager *appDataManager)
{
    app_data_manager_ = appDataManager;
}

void ConfWgt::loadThemePreference()
{
    if (!app_data_manager_)
    {
        return;
    }

    int themeType = app_data_manager_->getThemePreference();

    if (themeType == 0)
    {
        // 正常版
        ui->btn_normal->setChecked(true);
        ui->btn_simple->setChecked(false);
        ui->pic_normal->setStyleSheet(R"(border-image: url(:/img/theme_normal_checked.png);)");
        ui->pic_simple->setStyleSheet(R"(border-image: url(:/img/theme_simple.png);)");
    }
    else
    {
        // 简约版
        ui->btn_normal->setChecked(false);
        ui->btn_simple->setChecked(true);
        ui->pic_normal->setStyleSheet(R"(border-image: url(:/img/theme_normal.png);)");
        ui->pic_simple->setStyleSheet(R"(border-image: url(:/img/theme_simple_checked.png);)");
    }
}

void ConfWgt::showSwitchingStatus()
{
    status_label_->setText("正在切换...");

    // 设置标签位置在窗口中央
    int x = (width() - status_label_->sizeHint().width()) / 2;
    int y = (height() - status_label_->sizeHint().height()) / 2;
    status_label_->move(x, y);
    status_label_->adjustSize();
    status_label_->show();

    // 启动超时计时器，如果3秒内没有收到完成通知，自动显示完成
    status_timer_->start(3000);
}

void ConfWgt::showSwitchCompleteStatus()
{
    status_label_->setText("切换完成");
    status_label_->adjustSize();

    // 重新居中
    int x = (width() - status_label_->width()) / 2;
    int y = (height() - status_label_->height()) / 2;
    status_label_->move(x, y);

    // 1.5秒后隐藏
    QTimer::singleShot(1500, status_label_, &QLabel::hide);
}

void ConfWgt::onThemeSwitchCompleted()
{
    // 主题切换完成后调用此方法
    if (status_timer_->isActive())
    {
        status_timer_->stop();
    }
    showSwitchCompleteStatus();
}
