
## 总结

我已经成功将您的卡片点击事件系统从基于名称（name）的分发改为基于ID（cardId）的分发。主要改动包括：

### 1. **CardWgt类的改动**：
- 添加了`card_id_`成员变量
- 添加了`SetCardId(int id)`和`GetCardId() const`方法
- 修改了`cardClicked`信号，从`cardClicked(const QString &name)`改为`cardClicked(int cardId)`
- 修改了事件处理器，现在发送卡片ID而不是名称

### 2. **DxwsWgt类的改动**：
- 定义了`CardId`枚举，包含所有卡片的唯一ID
- 修改了所有`AddXXX`方法的签名，添加了`cardId`参数
- 修改了`OnCardClicked`槽函数，从`OnCardClicked(const QString &name)`改为`OnCardClicked(int cardId)`
- 使用`switch-case`语句替代了`if-else`链，通过ID进行分发

### 3. **卡片ID分配策略**：
- **基础卡片ID**：1001-1006（百度网盘、360云盘、老百度盘、阿里云盘、腾讯微云、拼音输入法）
- **收藏卡片ID**：使用基础ID + 偏移量
- **业务工具卡片ID**：使用基础ID + 100系列偏移量

### 4. **优势**：
- **唯一性**：每个卡片都有唯一的数字ID，避免名称重复问题
- **高效性**：使用`switch-case`比字符串比较更高效
- **可扩展性**：容易添加新的卡片类型，只需分配新的ID
- **国际化友好**：ID不受语言变化影响
- **调试友好**：通过ID可以快速定位具体的卡片

### 5. **兼容性处理**：
- 对于同一类型的多个卡片（如多个百度网盘卡片），使用ID范围进行处理
- 保留了原有的功能逻辑，只是改变了分发机制

现在当用户点击任何卡片时，系统会根据卡片的唯一ID来执行相应的操作，这比之前基于名称的方式更加可靠和高效。