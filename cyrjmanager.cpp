#include "cyrjmanager.h"
#include "cardids.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QProcess>
#include <QStandardPaths>

CyrjManager::<PERSON>r<PERSON><PERSON>anager(QWidget *parent) : BaseManager(parent)
{
}

CyrjManager::~CyrjManager()
{
    // 清理卡片
    for (CardWgt *card : cyrj_cards_)
    {
        delete card;
    }
}

void CyrjManager::InitCyrj()
{
    AddCyrj(":/img/Standard/img_software/ic_baidupan.png", ":/img/Lite/img_software/ic_baidupan_2.png", "百度网盘",
            CARD_ID_CYRJ_BAIDU_NETDISK);
    AddCyrj(":/img/Standard/img_software/ic_oidbaidudrive.png", ":/img/Lite/img_software/ic_oidbaidudrive_2.png",
            "老百度盘", CARD_ID_CYRJ_OLD_BAIDU);
    AddCyrj(":/img/Standard/img_software/ic_pinyin.png", ":/img/Lite/img_software/ic_pinyin_2.png", "拼音输入法",
            CARD_ID_CYRJ_PINYIN_INPUT);
    AddCyrj(":/img/Standard/img_software/ic_dbc_2000.png", ":/img/Lite/img_software/ic_dbc_2000_2.png", "DBC_2000",
            CARD_ID_CYRJ_DBC2000);
    AddCyrj(":/img/Standard/img_software/ic_commonbrowsers.png", ":/img/Lite/img_software/ic_commonbrowsers_2.png",
            "常用浏览器", CARD_ID_CYRJ_COMMON_BROWSERS);
    AddCyrj(":/img/Standard/img_software/ic_360clouddrive.png", ":/img/Lite/img_software/ic_360clouddrive_2.png",
            "360云盘", CARD_ID_CYRJ_360_CLOUD);
    AddCyrj(":/img/Standard/img_software/ic_tencentweiyun.png", ":/img/Lite/img_software/ic_tencentweiyun_2.png",
            "腾讯微云", CARD_ID_CYRJ_TENCENT_WEIYUN);
    AddCyrj(":/img/Standard/img_software/ic_wubi.png", ":/img/Lite/img_software/ic_wubi_2.png", "五笔输入法",
            CARD_ID_CYRJ_WUBI_INPUT);
    AddCyrj(":/img/Standard/img_software/ic_ms_sql.png", ":/img/Lite/img_software/ic_ms_sql_2.png", "MS_SQL",
            CARD_ID_CYRJ_MSSQL);
    AddCyrj(":/img/Standard/img_software/ic_customenvironment.png",
            ":/img/Lite/img_software/ic_customenvironment_2.png", "定制环境", CARD_ID_CYRJ_CUSTOM_ENV);
    AddCyrj(":/img/Standard/img_software/ic_alibabaclouddrive.png",
            ":/img/Lite/img_software/ic_alibabaclouddrive_2.png", "阿里云盘", CARD_ID_CYRJ_ALI_DRIVE);

    AddCyrj(":/img/Standard/img_software/ic_microsoftruntimelibrary.png",
            ":/img/Lite/img_software/ic_microsoftruntimelibrary_2.png", "微软运行库", CARD_ID_CYRJ_MS_RUNTIME);
    AddCyrj(":/img/Standard/img_software/ic_processexplorer.png", ":/img/Lite/img_software/ic_processexplorer_2.png",
            "ProcExp", CARD_ID_CYRJ_PROCESS_EXPLORER);
    AddCyrj(":/img/Standard/img_software/ic_displaydesktop.png", ":/img/Lite/img_software/ic_displaydesktop_2.png",
            "显示桌面图标", CARD_ID_CYRJ_SHOW_DESKTOP_ICONS);
}

void CyrjManager::AddCyrj(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId)
{
    CardWgt *card = new CardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetTheme(0);
    card->SetCardId(cardId);

    // 连接卡片点击信号
    connect(card, &CardWgt::cardClicked, this, &CyrjManager::cardClicked);
    connect(card, &CardWgt::collectClicked, this, &CyrjManager::collectClicked);

    cyrj_cards_.push_back(card);
}

void CyrjManager::HandleCardClick(int cardId)
{
    switch (cardId)
    {
    // 常用软件区域
    case CARD_ID_CYRJ_BAIDU_NETDISK:
        LaunchCyrjBaiduNetdisk();
        break;
    case CARD_ID_CYRJ_OLD_BAIDU:
        LaunchCyrjOldBaidu();
        break;
    case CARD_ID_CYRJ_PINYIN_INPUT:
        LaunchCyrjPinyinInput();
        break;
    case CARD_ID_CYRJ_DBC2000:
        LaunchCyrjDBC2000();
        break;
    case CARD_ID_CYRJ_COMMON_BROWSERS:
        LaunchCyrjCommonBrowsers();
        break;
    case CARD_ID_CYRJ_360_CLOUD:
        LaunchCyrj360Cloud();
        break;
    case CARD_ID_CYRJ_TENCENT_WEIYUN:
        LaunchCyrjTencentWeiyun();
        break;
    case CARD_ID_CYRJ_WUBI_INPUT:
        LaunchCyrjWubiInput();
        break;
    case CARD_ID_CYRJ_MSSQL:
        LaunchCyrjMSSQL();
        break;
    case CARD_ID_CYRJ_CUSTOM_ENV:
        LaunchCyrjCustomEnv();
        break;
    case CARD_ID_CYRJ_ALI_DRIVE:
        LaunchCyrjAliDrive();
        break;

    case CARD_ID_CYRJ_MS_RUNTIME:
        LaunchCyrjMSRuntime();
        break;
    case CARD_ID_CYRJ_PROCESS_EXPLORER:
        LaunchCyrjProcessExplorer();
        break;
    case CARD_ID_CYRJ_SHOW_DESKTOP_ICONS:
        LaunchCyrjShowDesktopIcons();
        break;
    }
}

void CyrjManager::LaunchCyrjBaiduNetdisk()
{
    ExecuteConfigFile("config/sub/software/BaiduNetdisk.bat", "百度网盘");
}

void CyrjManager::LaunchCyrjOldBaidu()
{
    ExecuteConfigFile("config/sub/software/OldBaiduDisk.bat", "老百度盘");
}

void CyrjManager::LaunchCyrjPinyinInput()
{
    ExecuteConfigFile("config/sub/software/PinyinInputIME.bat", "拼音输入法");
}

void CyrjManager::LaunchCyrjDBC2000()
{
    ExecuteConfigFile("config/sub/software/dbc2000.bat", "DBC_2000");
}

void CyrjManager::LaunchCyrjCommonBrowsers()
{
    ExecuteConfigFile("config/sub/software/CommonBrowsers.bat", "常用浏览器");
}

void CyrjManager::LaunchCyrj360Cloud()
{
    ExecuteConfigFile("config/sub/software/360CloudDisk.bat", "360云盘");
}

void CyrjManager::LaunchCyrjTencentWeiyun()
{
    ExecuteConfigFile("config/sub/software/TencentWeiyun.bat", "腾讯微云");
}

void CyrjManager::LaunchCyrjWubiInput()
{
    ExecuteConfigFile("config/sub/software/WubiInputIME.bat", "五笔输入法");
}

void CyrjManager::LaunchCyrjMSSQL()
{
    ExecuteConfigFile("config/sub/software/mssql.bat", "MS_SQL");
}

void CyrjManager::LaunchCyrjCustomEnv()
{
    ExecuteConfigFile("config/sub/software/CustomEnvironment.bat", "定制环境");
}

void CyrjManager::LaunchCyrjAliDrive()
{
    ExecuteConfigFile("config/sub/software/AliCloudDisk.bat", "阿里云盘");
}

void CyrjManager::LaunchCyrjMSRuntime()
{
    ExecuteConfigFile("config/sub/software/MicrosoftRuntime.bat", "微软运行库");
}

void CyrjManager::LaunchCyrjProcessExplorer()
{
    ExecuteConfigFile("config/sub/software/ProcessExplorer.bat", "ProcExp");
}

void CyrjManager::HandleCardCollect(int cardId, bool isCollected)
{
    // The actual logic is handled in DxwsWgt::OnCardCollectClicked
    // This function is here to fulfill the virtual function requirement if we make it pure virtual in the future.
    Q_UNUSED(cardId);
    Q_UNUSED(isCollected);
}

void CyrjManager::LaunchCyrjShowDesktopIcons()
{
    ExecuteConfigFile("config/sub/software/ShowDesktopIcons.bat", "显示桌面图标");
}
