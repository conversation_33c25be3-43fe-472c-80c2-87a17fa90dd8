#include "cardwgt.h"
#include "ui_cardwgt.h"

#include <QMouseEvent>

CardWgt::CardWgt(QWidget *parent) : QWidget(parent), ui(new Ui::CardWgt)
{
    ui->setupUi(this);

    ui->frame->installEventFilter(this);

    ui->btn_collect->hide();
}

CardWgt::~CardWgt()
{
    delete ui;
}

void CardWgt::setNormal_icon_path(const QString &normal_icon_path)
{
    normal_icon_path_ = normal_icon_path;
}

void CardWgt::setSimple_icon_path(const QString &simple_icon_path)
{
    simple_icon_path_ = simple_icon_path;
}

void CardWgt::SetTheme(int type)
{
    type_ = type;
    switch (type)
    {
    case 0: {
        SetIcon(normal_icon_path_);
    }
    break;
    case 1: {
        SetIcon(simple_icon_path_);
    }
    break;
    }
}

void CardWgt::SetName(QString name)
{
    ui->label_name->setText(name);
}

QString CardWgt::GetName()
{
    return ui->label_name->text();
}

QString CardWgt::GetNormalIconPath()
{
    return normal_icon_path_;
}

QString CardWgt::GetSimpleIconPath()
{
    return simple_icon_path_;
}

void CardWgt::SetCardId(int id)
{
    card_id_ = id;
}

int CardWgt::GetCardId() const
{
    return card_id_;
}

void CardWgt::SetCollected(bool collected)
{
    ui->btn_collect->setChecked(collected);
}

bool CardWgt::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == ui->frame)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

        switch (event->type())
        {
        case QEvent::Enter: {
            //            QPoint pos = mouseEvent->globalPos();
            //            QPoint pos_collect = ui->btn_collect->mapToGlobal(QPoint(0, 0));
            //            int w = ui->btn_collect->width();
            //            int h = ui->btn_collect->height();
            //            if(((pos.x() >= pos_collect.x()) && (pos.x() <= (pos_collect.x()+w))) &&
            //                   ((pos.y() >= pos_collect.y()) && (pos.y() <= (pos_collect.y()+h))))
            //            {
            //                ui->btn_collect->show();
            //            }
            //            else
            //            {
            //                ui->btn_collect->hide();
            //            }
            ui->btn_collect->show();
        }
        break;
        case QEvent::Leave: {
            ui->btn_collect->hide();
        }
        break;
        case QEvent::MouseButtonPress: {
            if (mouseEvent->button() == Qt::LeftButton)
            {
                // 添加按下效果 - 稍微缩小
                //    ui->frame->setStyleSheet("QFrame { transform: scale(0.95); }");
                emit cardClicked(card_id_); // 发送卡片ID而不是名称
                return true;
            }
        }
        break;
        // case QEvent::MouseButtonRelease: {
        //     if (mouseEvent->button() == Qt::LeftButton)
        //     {
        //         // 恢复正常大小
        //         ui->frame->setStyleSheet("");
        //         return true;
        //     }
        // }
        // break;
        default:
            break;
        }
    }

    // 其他事件交给基类处理
    return QWidget::eventFilter(watched, event);
}

void CardWgt::SetIcon(QString path)
{
    QString style_sheet("border-image:url(\"%1\")");
    ui->label_icon->setStyleSheet(style_sheet.arg(path));
}

void CardWgt::on_btn_collect_clicked()
{
    emit collectClicked(card_id_, ui->btn_collect->isChecked());
}
