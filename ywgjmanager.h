#ifndef YWGJMANAGER_H
#define YWGJMANAGER_H

#include "basemanager.h"
#include "cardids.h"
#include "cardwgt.h"
#include <QList>
#include <QObject>
#include <QWidget>

class YwgjManager : public BaseManager
{
    Q_OBJECT

  public:
    explicit YwgjManager(QWidget *parent = nullptr);
    ~YwgjManager();

    // 初始化运维工具相关功能
    void InitYwgj();

    // 添加工具卡片
    void AddYwgj(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId);

    // 获取卡片列表
    QList<CardWgt *> GetYwgjCards() const
    {
        return ywgj_cards_;
    }

    // 处理卡片点击事件
    void HandleCardClick(int cardId);

    // 处理卡片收藏事件
    void HandleCardCollect(int cardId, bool isCollected);

  private:
    // 运维工具区域功能函数
    void LaunchKMSActivate();
    void LaunchWinOptimize();
    void LaunchProgramAndFeatures();
    void LaunchNET35();
    void LaunchTimeSync();
    void LaunchIISDefault();
    void LaunchRestartExplorer();
    void LaunchPartitionAssist();
    void LaunchNET4X();
    void LaunchIISFull();
    void LaunchRestartClipboard();
    void LaunchVirtualCheck();
    void LaunchPowerOptions();
    void LaunchRestartComputer();


  private:
    QList<CardWgt *> ywgj_cards_;
};

#endif // YWGJMANAGER_H
