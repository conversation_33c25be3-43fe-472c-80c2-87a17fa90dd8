//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ 生成的包含文件。
// 供 DxwsApp_resource.rc 使用
//
#define SW_HIDE                         0
#define HIDE_WINDOW                     0
#define WM_NULL                         0x0000
#define WA_INACTIVE                     0
#define HTNOWHERE                       0
#define SMTO_NORMAL                     0x0000
#define ICON_SMALL                      0
#define SIZE_RESTORED                   0
#define BN_CLICKED                      0
#define BST_UNCHECKED                   0x0000
#define HDS_HORZ                        0x0000
#define TBSTYLE_BUTTON                  0x0000
#define TBS_HORZ                        0x0000
#define TBS_BOTTOM                      0x0000
#define TBS_RIGHT                       0x0000
#define LVS_ICON                        0x0000
#define LVS_ALIGNTOP                    0x0000
#define TCS_TABS                        0x0000
#define TCS_SINGLELINE                  0x0000
#define TCS_RIGHTJUSTIFY                0x0000
#define DTS_SHORTDATEFORMAT             0x0000
#define PGS_VERT                        0x00000000
#define LANG_NEUTRAL                    0x00
#define SUBLANG_NEUTRAL                 0x00
#define SORT_DEFAULT                    0x0
#define SORT_JAPANESE_XJIS              0x0
#define SORT_CHINESE_BIG5               0x0
#define SORT_CHINESE_PRCP               0x0
#define SORT_KOREAN_KSC                 0x0
#define SORT_HUNGARIAN_DEFAULT          0x0
#define SORT_GEORGIAN_TRADITIONAL       0x0
#define _USE_DECLSPECS_FOR_SAL          0
#define _USE_ATTRIBUTES_FOR_SAL         0
#define __drv_typeConst                 0
#define CREATEPROCESS_MANIFEST_RESOURCE_ID 1
#define MINIMUM_RESERVED_MANIFEST_RESOURCE_ID 1
#define SW_SHOWNORMAL                   1
#define SW_NORMAL                       1
#define SHOW_OPENWINDOW                 1
#define SW_PARENTCLOSING                1
#define VK_LBUTTON                      0x01
#define WM_CREATE                       0x0001
#define WA_ACTIVE                       1
#define PWR_OK                          1
#define PWR_SUSPENDREQUEST              1
#define NFR_ANSI                        1
#define UIS_SET                         1
#define UISF_HIDEFOCUS                  0x1
#define XBUTTON1                        0x0001
#define WMSZ_LEFT                       1
#define HTCLIENT                        1
#define SMTO_BLOCK                      0x0001
#define MA_ACTIVATE                     1
#define ICON_BIG                        1
#define SIZE_MINIMIZED                  1
#define MK_LBUTTON                      0x0001
#define TME_HOVER                       0x00000001
#define CS_VREDRAW                      0x0001
#define CF_TEXT                         1
#define SCF_ISSECURE                    0x00000001
#define IDOK                            1
#define BN_PAINT                        1
#define BST_CHECKED                     0x0001
#define TBSTYLE_SEP                     0x0001
#define TTS_ALWAYSTIP                   0x01
#define TBS_AUTOTICKS                   0x0001
#define UDS_WRAP                        0x0001
#define PBS_SMOOTH                      0x01
#define LWS_TRANSPARENT                 0x0001
#define LVS_REPORT                      0x0001
#define TVS_HASBUTTONS                  0x0001
#define TVS_EX_NOSINGLECOLLAPSE         0x0001
#define TCS_SCROLLOPPOSITE              0x0001
#define ACS_CENTER                      0x0001
#define MCS_DAYSTATE                    0x0001
#define DTS_UPDOWN                      0x0001
#define PGS_HORZ                        0x00000001
#define NFS_EDIT                        0x0001
#define BCSIF_GLYPH                     0x0001
#define BCSS_NOSPLIT                    0x0001
#define LANG_ARABIC                     0x01
#define SUBLANG_DEFAULT                 0x01
#define SUBLANG_AFRIKAANS_SOUTH_AFRICA  0x01
#define SUBLANG_ALBANIAN_ALBANIA        0x01
#define SUBLANG_ALSATIAN_FRANCE         0x01
#define SUBLANG_AMHARIC_ETHIOPIA        0x01
#define SUBLANG_ARABIC_SAUDI_ARABIA     0x01
#define SUBLANG_ARMENIAN_ARMENIA        0x01
#define SUBLANG_ASSAMESE_INDIA          0x01
#define SUBLANG_AZERI_LATIN             0x01
#define SUBLANG_AZERBAIJANI_AZERBAIJAN_LATIN 0x01
#define SUBLANG_BANGLA_INDIA            0x01
#define SUBLANG_BASHKIR_RUSSIA          0x01
#define SUBLANG_BASQUE_BASQUE           0x01
#define SUBLANG_BELARUSIAN_BELARUS      0x01
#define SUBLANG_BENGALI_INDIA           0x01
#define SUBLANG_BRETON_FRANCE           0x01
#define SUBLANG_BULGARIAN_BULGARIA      0x01
#define SUBLANG_CATALAN_CATALAN         0x01
#define SUBLANG_CENTRAL_KURDISH_IRAQ    0x01
#define SUBLANG_CHEROKEE_CHEROKEE       0x01
#define SUBLANG_CHINESE_TRADITIONAL     0x01
#define SUBLANG_CORSICAN_FRANCE         0x01
#define SUBLANG_CZECH_CZECH_REPUBLIC    0x01
#define SUBLANG_CROATIAN_CROATIA        0x01
#define SUBLANG_DANISH_DENMARK          0x01
#define SUBLANG_DARI_AFGHANISTAN        0x01
#define SUBLANG_DIVEHI_MALDIVES         0x01
#define SUBLANG_DUTCH                   0x01
#define SUBLANG_ENGLISH_US              0x01
#define SUBLANG_ESTONIAN_ESTONIA        0x01
#define SUBLANG_FAEROESE_FAROE_ISLANDS  0x01
#define SUBLANG_FILIPINO_PHILIPPINES    0x01
#define SUBLANG_FINNISH_FINLAND         0x01
#define SUBLANG_FRENCH                  0x01
#define SUBLANG_FRISIAN_NETHERLANDS     0x01
#define SUBLANG_GALICIAN_GALICIAN       0x01
#define SUBLANG_GEORGIAN_GEORGIA        0x01
#define SUBLANG_GERMAN                  0x01
#define SUBLANG_GREEK_GREECE            0x01
#define SUBLANG_GREENLANDIC_GREENLAND   0x01
#define SUBLANG_GUJARATI_INDIA          0x01
#define SUBLANG_HAUSA_NIGERIA_LATIN     0x01
#define SUBLANG_HAWAIIAN_US             0x01
#define SUBLANG_HEBREW_ISRAEL           0x01
#define SUBLANG_HINDI_INDIA             0x01
#define SUBLANG_HUNGARIAN_HUNGARY       0x01
#define SUBLANG_ICELANDIC_ICELAND       0x01
#define SUBLANG_IGBO_NIGERIA            0x01
#define SUBLANG_INDONESIAN_INDONESIA    0x01
#define SUBLANG_INUKTITUT_CANADA        0x01
#define SUBLANG_ITALIAN                 0x01
#define SUBLANG_JAPANESE_JAPAN          0x01
#define SUBLANG_KANNADA_INDIA           0x01
#define SUBLANG_KAZAK_KAZAKHSTAN        0x01
#define SUBLANG_KHMER_CAMBODIA          0x01
#define SUBLANG_KICHE_GUATEMALA         0x01
#define SUBLANG_KINYARWANDA_RWANDA      0x01
#define SUBLANG_KONKANI_INDIA           0x01
#define SUBLANG_KOREAN                  0x01
#define SUBLANG_KYRGYZ_KYRGYZSTAN       0x01
#define SUBLANG_LAO_LAO                 0x01
#define SUBLANG_LATVIAN_LATVIA          0x01
#define SUBLANG_LITHUANIAN              0x01
#define SUBLANG_LUXEMBOURGISH_LUXEMBOURG 0x01
#define SUBLANG_MACEDONIAN_MACEDONIA    0x01
#define SUBLANG_MALAY_MALAYSIA          0x01
#define SUBLANG_MALAYALAM_INDIA         0x01
#define SUBLANG_MALTESE_MALTA           0x01
#define SUBLANG_MAORI_NEW_ZEALAND       0x01
#define SUBLANG_MAPUDUNGUN_CHILE        0x01
#define SUBLANG_MARATHI_INDIA           0x01
#define SUBLANG_MOHAWK_MOHAWK           0x01
#define SUBLANG_MONGOLIAN_CYRILLIC_MONGOLIA 0x01
#define SUBLANG_NEPALI_NEPAL            0x01
#define SUBLANG_NORWEGIAN_BOKMAL        0x01
#define SUBLANG_OCCITAN_FRANCE          0x01
#define SUBLANG_ODIA_INDIA              0x01
#define SUBLANG_ORIYA_INDIA             0x01
#define SUBLANG_PASHTO_AFGHANISTAN      0x01
#define SUBLANG_PERSIAN_IRAN            0x01
#define SUBLANG_POLISH_POLAND           0x01
#define SUBLANG_PORTUGUESE_BRAZILIAN    0x01
#define SUBLANG_PUNJABI_INDIA           0x01
#define SUBLANG_QUECHUA_BOLIVIA         0x01
#define SUBLANG_ROMANIAN_ROMANIA        0x01
#define SUBLANG_ROMANSH_SWITZERLAND     0x01
#define SUBLANG_RUSSIAN_RUSSIA          0x01
#define SUBLANG_SAKHA_RUSSIA            0x01
#define SUBLANG_SAMI_NORTHERN_NORWAY    0x01
#define SUBLANG_SANSKRIT_INDIA          0x01
#define SUBLANG_SCOTTISH_GAELIC         0x01
#define SUBLANG_SERBIAN_CROATIA         0x01
#define SUBLANG_SINDHI_INDIA            0x01
#define SUBLANG_SINHALESE_SRI_LANKA     0x01
#define SUBLANG_SOTHO_NORTHERN_SOUTH_AFRICA 0x01
#define SUBLANG_SLOVAK_SLOVAKIA         0x01
#define SUBLANG_SLOVENIAN_SLOVENIA      0x01
#define SUBLANG_SPANISH                 0x01
#define SUBLANG_SWAHILI_KENYA           0x01
#define SUBLANG_SWEDISH                 0x01
#define SUBLANG_SYRIAC_SYRIA            0x01
#define SUBLANG_TAJIK_TAJIKISTAN        0x01
#define SUBLANG_TAMIL_INDIA             0x01
#define SUBLANG_TATAR_RUSSIA            0x01
#define SUBLANG_TELUGU_INDIA            0x01
#define SUBLANG_THAI_THAILAND           0x01
#define SUBLANG_TIBETAN_PRC             0x01
#define SUBLANG_TIGRINYA_ETHIOPIA       0x01
#define SUBLANG_TSWANA_SOUTH_AFRICA     0x01
#define SUBLANG_TURKISH_TURKEY          0x01
#define SUBLANG_TURKMEN_TURKMENISTAN    0x01
#define SUBLANG_UIGHUR_PRC              0x01
#define SUBLANG_UKRAINIAN_UKRAINE       0x01
#define SUBLANG_UPPER_SORBIAN_GERMANY   0x01
#define SUBLANG_URDU_PAKISTAN           0x01
#define SUBLANG_UZBEK_LATIN             0x01
#define SUBLANG_VIETNAMESE_VIETNAM      0x01
#define SUBLANG_WELSH_UNITED_KINGDOM    0x01
#define SUBLANG_WOLOF_SENEGAL           0x01
#define SUBLANG_XHOSA_SOUTH_AFRICA      0x01
#define SUBLANG_YAKUT_RUSSIA            0x01
#define SUBLANG_YI_PRC                  0x01
#define SUBLANG_YORUBA_NIGERIA          0x01
#define SUBLANG_ZULU_SOUTH_AFRICA       0x01
#define SORT_INVARIANT_MATH             0x1
#define SORT_JAPANESE_UNICODE           0x1
#define SORT_CHINESE_UNICODE            0x1
#define SORT_KOREAN_UNICODE             0x1
#define SORT_GERMAN_PHONE_BOOK          0x1
#define SORT_HUNGARIAN_TECHNICAL        0x1
#define SORT_GEORGIAN_MODERN            0x1
#define __drv_typeCond                  1
#define VS_VERSION_INFO                 1
#define VFFF_ISSHAREDFILE               0x0001
#define VFF_CURNEDEST                   0x0001
#define VIFF_FORCEINSTALL               0x0001
#define WINAPI_FAMILY_PC_APP            2
#define ISOLATIONAWARE_MANIFEST_RESOURCE_ID 2
#define SW_SHOWMINIMIZED                2
#define SHOW_ICONWINDOW                 2
#define SW_OTHERZOOM                    2
#define VK_RBUTTON                      0x02
#define WM_DESTROY                      0x0002
#define WA_CLICKACTIVE                  2
#define PWR_SUSPENDRESUME               2
#define NFR_UNICODE                     2
#define UIS_CLEAR                       2
#define UISF_HIDEACCEL                  0x2
#define XBUTTON2                        0x0002
#define WMSZ_RIGHT                      2
#define HTCAPTION                       2
#define SMTO_ABORTIFHUNG                0x0002
#define MA_ACTIVATEANDEAT               2
#define ICON_SMALL2                     2
#define SIZE_MAXIMIZED                  2
#define MK_RBUTTON                      0x0002
#define TME_LEAVE                       0x00000002
#define CS_HREDRAW                      0x0002
#define CF_BITMAP                       2
#define IDCANCEL                        2
#define BN_HILITE                       2
#define BST_INDETERMINATE               0x0002
#define HDS_BUTTONS                     0x0002
#define TBSTYLE_CHECK                   0x0002
#define TTS_NOPREFIX                    0x02
#define TBS_VERT                        0x0002
#define UDS_SETBUDDYINT                 0x0002
#define LWS_IGNORERETURN                0x0002
#define LVS_SMALLICON                   0x0002
#define TVS_HASLINES                    0x0002
#define TVS_EX_MULTISELECT              0x0002
#define TCS_BOTTOM                      0x0002
#define TCS_RIGHT                       0x0002
#define ACS_TRANSPARENT                 0x0002
#define MCS_MULTISELECT                 0x0002
#define DTS_SHOWNONE                    0x0002
#define PGS_AUTOSCROLL                  0x00000002
#define NFS_STATIC                      0x0002
#define BCSIF_IMAGE                     0x0002
#define BCSS_STRETCH                    0x0002
#define LANG_BULGARIAN                  0x02
#define SUBLANG_SYS_DEFAULT             0x02
#define SUBLANG_ARABIC_IRAQ             0x02
#define SUBLANG_AZERI_CYRILLIC          0x02
#define SUBLANG_AZERBAIJANI_AZERBAIJAN_CYRILLIC 0x02
#define SUBLANG_BANGLA_BANGLADESH       0x02
#define SUBLANG_BENGALI_BANGLADESH      0x02
#define SUBLANG_CHINESE_SIMPLIFIED      0x02
#define SUBLANG_DUTCH_BELGIAN           0x02
#define SUBLANG_ENGLISH_UK              0x02
#define SUBLANG_FRENCH_BELGIAN          0x02
#define SUBLANG_FULAH_SENEGAL           0x02
#define SUBLANG_GERMAN_SWISS            0x02
#define SUBLANG_INUKTITUT_CANADA_LATIN  0x02
#define SUBLANG_IRISH_IRELAND           0x02
#define SUBLANG_ITALIAN_SWISS           0x02
#define SUBLANG_KASHMIRI_SASIA          0x02
#define SUBLANG_KASHMIRI_INDIA          0x02
#define SUBLANG_LOWER_SORBIAN_GERMANY   0x02
#define SUBLANG_MALAY_BRUNEI_DARUSSALAM 0x02
#define SUBLANG_MONGOLIAN_PRC           0x02
#define SUBLANG_NEPALI_INDIA            0x02
#define SUBLANG_NORWEGIAN_NYNORSK       0x02
#define SUBLANG_PORTUGUESE              0x02
#define SUBLANG_PULAR_SENEGAL           0x02
#define SUBLANG_PUNJABI_PAKISTAN        0x02
#define SUBLANG_QUECHUA_ECUADOR         0x02
#define SUBLANG_SAMI_NORTHERN_SWEDEN    0x02
#define SUBLANG_SERBIAN_LATIN           0x02
#define SUBLANG_SINDHI_PAKISTAN         0x02
#define SUBLANG_SINDHI_AFGHANISTAN      0x02
#define SUBLANG_SPANISH_MEXICAN         0x02
#define SUBLANG_SWEDISH_FINLAND         0x02
#define SUBLANG_TAMAZIGHT_ALGERIA_LATIN 0x02
#define SUBLANG_TAMIL_SRI_LANKA         0x02
#define SUBLANG_TIGRIGNA_ERITREA        0x02
#define SUBLANG_TIGRINYA_ERITREA        0x02
#define SUBLANG_TSWANA_BOTSWANA         0x02
#define SUBLANG_URDU_INDIA              0x02
#define SUBLANG_UZBEK_CYRILLIC          0x02
#define SUBLANG_VALENCIAN_VALENCIA      0x02
#define SORT_CHINESE_PRC                0x2
#define __drv_typeBitset                2
#define VFF_FILEINUSE                   0x0002
#define VIFF_DONTDELETEOLD              0x0002
#define WINAPI_FAMILY_PHONE_APP         3
#define ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID 3
#define SW_SHOWMAXIMIZED                3
#define SW_MAXIMIZE                     3
#define SHOW_FULLSCREEN                 3
#define SW_PARENTOPENING                3
#define VK_CANCEL                       0x03
#define WM_MOVE                         0x0003
#define PWR_CRITICALRESUME              3
#define NF_QUERY                        3
#define UIS_INITIALIZE                  3
#define WMSZ_TOP                        3
#define HTSYSMENU                       3
#define MA_NOACTIVATE                   3
#define SIZE_MAXSHOW                    3
#define CF_METAFILEPICT                 3
#define IDABORT                         3
#define BN_UNHILITE                     3
#define LVS_LIST                        0x0003
#define LVS_TYPEMASK                    0x0003
#define LANG_CATALAN                    0x03
#define LANG_VALENCIAN                  0x03
#define SUBLANG_CUSTOM_DEFAULT          0x03
#define SUBLANG_ARABIC_EGYPT            0x03
#define SUBLANG_CHINESE_HONGKONG        0x03
#define SUBLANG_ENGLISH_AUS             0x03
#define SUBLANG_FRENCH_CANADIAN         0x03
#define SUBLANG_GERMAN_AUSTRIAN         0x03
#define SUBLANG_QUECHUA_PERU            0x03
#define SUBLANG_SAMI_NORTHERN_FINLAND   0x03
#define SUBLANG_SERBIAN_CYRILLIC        0x03
#define SUBLANG_SPANISH_MODERN          0x03
#define SORT_CHINESE_BOPOMOFO           0x3
#define __drv_typeExpr                  3
#define WINAPI_FAMILY_SYSTEM            4
#define ISOLATIONPOLICY_MANIFEST_RESOURCE_ID 4
#define SW_SHOWNOACTIVATE               4
#define SHOW_OPENNOACTIVATE             4
#define SW_OTHERUNZOOM                  4
#define VK_MBUTTON                      0x04
#define NF_REQUERY                      4
#define UISF_ACTIVE                     0x4
#define WMSZ_TOPLEFT                    4
#define HTGROWBOX                       4
#define MA_NOACTIVATEANDEAT             4
#define SIZE_MAXHIDE                    4
#define MK_SHIFT                        0x0004
#define CF_SYLK                         4
#define IDRETRY                         4
#define BN_DISABLE                      4
#define BST_PUSHED                      0x0004
#define HDS_HOTTRACK                    0x0004
#define TBSTYLE_GROUP                   0x0004
#define TBS_TOP                         0x0004
#define TBS_LEFT                        0x0004
#define UDS_ALIGNRIGHT                  0x0004
#define PBS_VERTICAL                    0x04
#define LWS_NOPREFIX                    0x0004
#define LVS_SINGLESEL                   0x0004
#define TVS_LINESATROOT                 0x0004
#define TVS_EX_DOUBLEBUFFER             0x0004
#define TCS_MULTISELECT                 0x0004
#define ACS_AUTOPLAY                    0x0004
#define MCS_WEEKNUMBERS                 0x0004
#define DTS_LONGDATEFORMAT              0x0004
#define PGS_DRAGNDROP                   0x00000004
#define NFS_LISTCOMBO                   0x0004
#define BCSIF_STYLE                     0x0004
#define BCSS_ALIGNLEFT                  0x0004
#define LANG_CHINESE                    0x04
#define LANG_CHINESE_SIMPLIFIED         0x04
#define SUBLANG_CUSTOM_UNSPECIFIED      0x04
#define SUBLANG_ARABIC_LIBYA            0x04
#define SUBLANG_CHINESE_SINGAPORE       0x04
#define SUBLANG_CROATIAN_BOSNIA_HERZEGOVINA_LATIN 0x04
#define SUBLANG_ENGLISH_CAN             0x04
#define SUBLANG_FRENCH_SWISS            0x04
#define SUBLANG_GERMAN_LUXEMBOURG       0x04
#define SUBLANG_SAMI_LULE_NORWAY        0x04
#define SUBLANG_SPANISH_GUATEMALA       0x04
#define SUBLANG_TAMAZIGHT_MOROCCO_TIFINAGH 0x04
#define SORT_JAPANESE_RADICALSTROKE     0x4
#define SORT_CHINESE_RADICALSTROKE      0x4
#define VFF_BUFFTOOSMALL                0x0004
#define WINAPI_FAMILY_SERVER            5
#define ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID 5
#define SW_SHOW                         5
#define VK_XBUTTON1                     0x05
#define WM_SIZE                         0x0005
#define WMSZ_TOPRIGHT                   5
#define HTMENU                          5
#define CF_DIF                          5
#define IDIGNORE                        5
#define BN_DOUBLECLICKED                5
#define LANG_CZECH                      0x05
#define SUBLANG_UI_CUSTOM_DEFAULT       0x05
#define SUBLANG_ARABIC_ALGERIA          0x05
#define SUBLANG_BOSNIAN_BOSNIA_HERZEGOVINA_LATIN 0x05
#define SUBLANG_CHINESE_MACAU           0x05
#define SUBLANG_ENGLISH_NZ              0x05
#define SUBLANG_FRENCH_LUXEMBOURG       0x05
#define SUBLANG_GERMAN_LIECHTENSTEIN    0x05
#define SUBLANG_SAMI_LULE_SWEDEN        0x05
#define SUBLANG_SPANISH_COSTA_RICA      0x05
#define WINAPI_FAMILY_GAMES             6
#define SW_MINIMIZE                     6
#define VK_XBUTTON2                     0x06
#define WM_ACTIVATE                     0x0006
#define WMSZ_BOTTOM                     6
#define HTHSCROLL                       6
#define CF_TIFF                         6
#define IDYES                           6
#define BN_SETFOCUS                     6
#define LANG_DANISH                     0x06
#define SUBLANG_ARABIC_MOROCCO          0x06
#define SUBLANG_ENGLISH_EIRE            0x06
#define SUBLANG_FRENCH_MONACO           0x06
#define SUBLANG_SAMI_SOUTHERN_NORWAY    0x06
#define SUBLANG_SERBIAN_BOSNIA_HERZEGOVINA_LATIN 0x06
#define SUBLANG_SPANISH_PANAMA          0x06
#define SW_SHOWMINNOACTIVE              7
#define WM_SETFOCUS                     0x0007
#define WMSZ_BOTTOMLEFT                 7
#define HTVSCROLL                       7
#define CF_OEMTEXT                      7
#define IDNO                            7
#define BN_KILLFOCUS                    7
#define LANG_GERMAN                     0x07
#define SUBLANG_ARABIC_TUNISIA          0x07
#define SUBLANG_ENGLISH_SOUTH_AFRICA    0x07
#define SUBLANG_SAMI_SOUTHERN_SWEDEN    0x07
#define SUBLANG_SERBIAN_BOSNIA_HERZEGOVINA_CYRILLIC 0x07
#define SUBLANG_SPANISH_DOMINICAN_REPUBLIC 0x07
#define SW_SHOWNA                       8
#define VK_BACK                         0x08
#define WM_KILLFOCUS                    0x0008
#define WMSZ_BOTTOMRIGHT                8
#define HTMINBUTTON                     8
#define SMTO_NOTIMEOUTIFNOTHUNG         0x0008
#define MK_CONTROL                      0x0008
#define CS_DBLCLKS                      0x0008
#define CF_DIB                          8
#define IDCLOSE                         8
#define BST_FOCUS                       0x0008
#define HDS_HIDDEN                      0x0008
#define TBSTYLE_DROPDOWN                0x0008
#define TBS_BOTH                        0x0008
#define UDS_ALIGNLEFT                   0x0008
#define PBS_MARQUEE                     0x08
#define LWS_USEVISUALSTYLE              0x0008
#define LVS_SHOWSELALWAYS               0x0008
#define TVS_EDITLABELS                  0x0008
#define TVS_EX_NOINDENTSTATE            0x0008
#define TCS_FLATBUTTONS                 0x0008
#define ACS_TIMER                       0x0008
#define MCS_NOTODAYCIRCLE               0x0008
#define NFS_BUTTON                      0x0008
#define BCSIF_SIZE                      0x0008
#define BCSS_IMAGE                      0x0008
#define LANG_GREEK                      0x08
#define SUBLANG_ARABIC_OMAN             0x08
#define SUBLANG_BOSNIAN_BOSNIA_HERZEGOVINA_CYRILLIC 0x08
#define SUBLANG_ENGLISH_JAMAICA         0x08
#define SUBLANG_SAMI_SKOLT_FINLAND      0x08
#define SUBLANG_SPANISH_VENEZUELA       0x08
#define SW_RESTORE                      9
#define VK_TAB                          0x09
#define HTMAXBUTTON                     9
#define CF_PALETTE                      9
#define IDHELP                          9
#define DTS_TIMEFORMAT                  0x0009
#define LANG_ENGLISH                    0x09
#define SUBLANG_ARABIC_YEMEN            0x09
#define SUBLANG_ENGLISH_CARIBBEAN       0x09
#define SUBLANG_SAMI_INARI_FINLAND      0x09
#define SUBLANG_SERBIAN_SERBIA_LATIN    0x09
#define SUBLANG_SPANISH_COLOMBIA        0x09
#define SW_SHOWDEFAULT                  10
#define WM_ENABLE                       0x000A
#define HTLEFT                          10
#define CF_PENDATA                      10
#define IDTRYAGAIN                      10
#define HELP_CONTEXTMENU                0x000a
#define LANG_SPANISH                    0x0a
#define SUBLANG_ARABIC_SYRIA            0x0a
#define SUBLANG_ENGLISH_BELIZE          0x0a
#define SUBLANG_SERBIAN_SERBIA_CYRILLIC 0x0a
#define SUBLANG_SPANISH_PERU            0x0a
#define SW_FORCEMINIMIZE                11
#define SW_MAX                          11
#define WM_SETREDRAW                    0x000B
#define HTRIGHT                         11
#define CF_RIFF                         11
#define IDCONTINUE                      11
#define HELP_FINDER                     0x000b
#define LANG_FINNISH                    0x0b
#define SUBLANG_ARABIC_JORDAN           0x0b
#define SUBLANG_ENGLISH_TRINIDAD        0x0b
#define SUBLANG_SERBIAN_MONTENEGRO_LATIN 0x0b
#define SUBLANG_SPANISH_ARGENTINA       0x0b
#define VK_CLEAR                        0x0C
#define WM_SETTEXT                      0x000C
#define HTTOP                           12
#define CF_WAVE                         12
#define HELP_WM_HELP                    0x000c
#define DTS_SHORTDATECENTURYFORMAT      0x000C
#define LANG_FRENCH                     0x0c
#define SUBLANG_ARABIC_LEBANON          0x0c
#define SUBLANG_ENGLISH_ZIMBABWE        0x0c
#define SUBLANG_SERBIAN_MONTENEGRO_CYRILLIC 0x0c
#define SUBLANG_SPANISH_ECUADOR         0x0c
#define VK_RETURN                       0x0D
#define WM_GETTEXT                      0x000D
#define HTTOPLEFT                       13
#define CF_UNICODETEXT                  13
#define HELP_SETPOPUP_POS               0x000d
#define LANG_HEBREW                     0x0d
#define SUBLANG_ARABIC_KUWAIT           0x0d
#define SUBLANG_ENGLISH_PHILIPPINES     0x0d
#define SUBLANG_SPANISH_CHILE           0x0d
#define WM_GETTEXTLENGTH                0x000E
#define HTTOPRIGHT                      14
#define CF_ENHMETAFILE                  14
#define LANG_HUNGARIAN                  0x0e
#define SUBLANG_ARABIC_UAE              0x0e
#define SUBLANG_SPANISH_URUGUAY         0x0e
#define WM_PAINT                        0x000F
#define HTBOTTOM                        15
#define CF_HDROP                        15
#define LANG_ICELANDIC                  0x0f
#define SUBLANG_ARABIC_BAHRAIN          0x0f
#define SUBLANG_SPANISH_PARAGUAY        0x0f
#define MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID 16
#define VK_SHIFT                        0x10
#define WM_CLOSE                        0x0010
#define HTBOTTOMLEFT                    16
#define WVR_ALIGNTOP                    0x0010
#define MK_MBUTTON                      0x0010
#define TME_NONCLIENT                   0x00000010
#define CF_LOCALE                       16
#define HELP_TCARD_DATA                 0x0010
#define TBSTYLE_AUTOSIZE                0x0010
#define TTS_NOANIMATE                   0x10
#define TBS_NOTICKS                     0x0010
#define UDS_AUTOBUDDY                   0x0010
#define PBS_SMOOTHREVERSE               0x10
#define LWS_USECUSTOMTEXT               0x0010
#define LVS_SORTASCENDING               0x0010
#define TVS_DISABLEDRAGDROP             0x0010
#define TVS_EX_RICHTOOLTIP              0x0010
#define TCS_FORCEICONLEFT               0x0010
#define MCS_NOTODAY                     0x0010
#define DTS_APPCANPARSE                 0x0010
#define NFS_ALL                         0x0010
#define LANG_ITALIAN                    0x10
#define SUBLANG_ARABIC_QATAR            0x10
#define SUBLANG_ENGLISH_INDIA           0x10
#define SUBLANG_SPANISH_BOLIVIA         0x10
#define VK_CONTROL                      0x11
#define WM_QUERYENDSESSION              0x0011
#define HTBOTTOMRIGHT                   17
#define CF_DIBV5                        17
#define HELP_TCARD_OTHER_CALLER         0x0011
#define LANG_JAPANESE                   0x11
#define SUBLANG_ENGLISH_MALAYSIA        0x11
#define SUBLANG_SPANISH_EL_SALVADOR     0x11
#define VK_MENU                         0x12
#define WM_QUIT                         0x0012
#define HTBORDER                        18
#define CF_MAX                          18
#define LANG_KOREAN                     0x12
#define SUBLANG_ENGLISH_SINGAPORE       0x12
#define SUBLANG_SPANISH_HONDURAS        0x12
#define VK_PAUSE                        0x13
#define WM_QUERYOPEN                    0x0013
#define HTOBJECT                        19
#define LANG_DUTCH                      0x13
#define SUBLANG_SPANISH_NICARAGUA       0x13
#define VK_CAPITAL                      0x14
#define WM_ERASEBKGND                   0x0014
#define HTCLOSE                         20
#define LANG_NORWEGIAN                  0x14
#define SUBLANG_SPANISH_PUERTO_RICO     0x14
#define _SAL_VERSION                    20
#define VK_KANA                         0x15
#define VK_HANGEUL                      0x15
#define VK_HANGUL                       0x15
#define WM_SYSCOLORCHANGE               0x0015
#define HTHELP                          21
#define LANG_POLISH                     0x15
#define SUBLANG_SPANISH_US              0x15
#define VK_IME_ON                       0x16
#define WM_ENDSESSION                   0x0016
#define LANG_PORTUGUESE                 0x16
#define VK_JUNJA                        0x17
#define LANG_ROMANSH                    0x17
#define RT_MANIFEST                     24
#define VK_FINAL                        0x18
#define WM_SHOWWINDOW                   0x0018
#define LANG_ROMANIAN                   0x18
#define VK_HANJA                        0x19
#define VK_KANJI                        0x19
#define LANG_RUSSIAN                    0x19
#define VK_IME_OFF                      0x1A
#define WM_WININICHANGE                 0x001A
#define LANG_BOSNIAN                    0x1a
#define LANG_CROATIAN                   0x1a
#define LANG_SERBIAN                    0x1a
#define VK_ESCAPE                       0x1B
#define WM_DEVMODECHANGE                0x001B
#define LANG_SLOVAK                     0x1b
#define VK_CONVERT                      0x1C
#define WM_ACTIVATEAPP                  0x001C
#define LANG_ALBANIAN                   0x1c
#define VK_NONCONVERT                   0x1D
#define WM_FONTCHANGE                   0x001D
#define LANG_SWEDISH                    0x1d
#define VK_ACCEPT                       0x1E
#define WM_TIMECHANGE                   0x001E
#define LANG_THAI                       0x1e
#define VK_MODECHANGE                   0x1F
#define WM_CANCELMODE                   0x001F
#define LANG_TURKISH                    0x1f
#define VK_SPACE                        0x20
#define WM_SETCURSOR                    0x0020
#define SMTO_ERRORONEXIT                0x0020
#define WVR_ALIGNLEFT                   0x0020
#define MK_XBUTTON1                     0x0020
#define CS_OWNDC                        0x0020
#define TBSTYLE_NOPREFIX                0x0020
#define TTS_NOFADE                      0x20
#define TBS_ENABLESELRANGE              0x0020
#define UDS_ARROWKEYS                   0x0020
#define LWS_RIGHT                       0x0020
#define LVS_SORTDESCENDING              0x0020
#define TVS_SHOWSELALWAYS               0x0020
#define TVS_EX_AUTOHSCROLL              0x0020
#define TCS_FORCELABELLEFT              0x0020
#define DTS_RIGHTALIGN                  0x0020
#define NFS_USEFONTASSOC                0x0020
#define LANG_URDU                       0x20
#define VK_PRIOR                        0x21
#define WM_MOUSEACTIVATE                0x0021
#define LANG_INDONESIAN                 0x21
#define VK_NEXT                         0x22
#define WM_CHILDACTIVATE                0x0022
#define LANG_UKRAINIAN                  0x22
#define VK_END                          0x23
#define WM_QUEUESYNC                    0x0023
#define LANG_BELARUSIAN                 0x23
#define VK_HOME                         0x24
#define WM_GETMINMAXINFO                0x0024
#define LANG_SLOVENIAN                  0x24
#define VK_LEFT                         0x25
#define LANG_ESTONIAN                   0x25
#define VK_UP                           0x26
#define WM_PAINTICON                    0x0026
#define LANG_LATVIAN                    0x26
#define VK_RIGHT                        0x27
#define WM_ICONERASEBKGND               0x0027
#define LANG_LITHUANIAN                 0x27
#define VK_DOWN                         0x28
#define WM_NEXTDLGCTL                   0x0028
#define LANG_TAJIK                      0x28
#define VK_SELECT                       0x29
#define LANG_FARSI                      0x29
#define LANG_PERSIAN                    0x29
#define VK_PRINT                        0x2A
#define WM_SPOOLERSTATUS                0x002A
#define LANG_VIETNAMESE                 0x2a
#define VK_EXECUTE                      0x2B
#define WM_DRAWITEM                     0x002B
#define LANG_ARMENIAN                   0x2b
#define VK_SNAPSHOT                     0x2C
#define WM_MEASUREITEM                  0x002C
#define LANG_AZERI                      0x2c
#define LANG_AZERBAIJANI                0x2c
#define VK_INSERT                       0x2D
#define WM_DELETEITEM                   0x002D
#define LANG_BASQUE                     0x2d
#define VK_DELETE                       0x2E
#define WM_VKEYTOITEM                   0x002E
#define LANG_LOWER_SORBIAN              0x2e
#define LANG_UPPER_SORBIAN              0x2e
#define VK_HELP                         0x2F
#define WM_CHARTOITEM                   0x002F
#define LANG_MACEDONIAN                 0x2f
#define WM_SETFONT                      0x0030
#define WM_GETFONT                      0x0031
#define WM_SETHOTKEY                    0x0032
#define LANG_TSWANA                     0x32
#define WM_GETHOTKEY                    0x0033
#define LANG_XHOSA                      0x34
#define LANG_ZULU                       0x35
#define LANG_AFRIKAANS                  0x36
#define WM_QUERYDRAGICON                0x0037
#define LANG_GEORGIAN                   0x37
#define LANG_FAEROESE                   0x38
#define WM_COMPAREITEM                  0x0039
#define LANG_HINDI                      0x39
#define LANG_MALTESE                    0x3a
#define LANG_SAMI                       0x3b
#define LANG_IRISH                      0x3c
#define WM_GETOBJECT                    0x003D
#define LANG_MALAY                      0x3e
#define LANG_KAZAK                      0x3f
#define WVR_ALIGNBOTTOM                 0x0040
#define MK_XBUTTON2                     0x0040
#define CS_CLASSDC                      0x0040
#define HDS_DRAGDROP                    0x0040
#define BTNS_SHOWTEXT                   0x0040
#define TTS_BALLOON                     0x40
#define TBS_FIXEDLENGTH                 0x0040
#define UDS_HORZ                        0x0040
#define LVS_SHAREIMAGELISTS             0x0040
#define TVS_RTLREADING                  0x0040
#define TVS_EX_FADEINOUTEXPANDOS        0x0040
#define TCS_HOTTRACK                    0x0040
#define MCS_NOTRAILINGDATES             0x0040
#define LANG_KYRGYZ                     0x40
#define WM_COMPACTING                   0x0041
#define LANG_SWAHILI                    0x41
#define LANG_TURKMEN                    0x42
#define LANG_UZBEK                      0x43
#define WM_COMMNOTIFY                   0x0044
#define LANG_TATAR                      0x44
#define LANG_BANGLA                     0x45
#define LANG_BENGALI                    0x45
#define WM_WINDOWPOSCHANGING            0x0046
#define LANG_PUNJABI                    0x46
#define WM_WINDOWPOSCHANGED             0x0047
#define LANG_GUJARATI                   0x47
#define WM_POWER                        0x0048
#define LANG_ODIA                       0x48
#define LANG_ORIYA                      0x48
#define LANG_TAMIL                      0x49
#define WM_COPYDATA                     0x004A
#define LANG_TELUGU                     0x4a
#define WM_CANCELJOURNAL                0x004B
#define LANG_KANNADA                    0x4b
#define LANG_MALAYALAM                  0x4c
#define LANG_ASSAMESE                   0x4d
#define WM_NOTIFY                       0x004E
#define LANG_MARATHI                    0x4e
#define LANG_SANSKRIT                   0x4f
#define WM_INPUTLANGCHANGEREQUEST       0x0050
#define LANG_MONGOLIAN                  0x50
#define WM_INPUTLANGCHANGE              0x0051
#define LANG_TIBETAN                    0x51
#define WM_TCARD                        0x0052
#define LANG_WELSH                      0x52
#define WM_HELP                         0x0053
#define LANG_KHMER                      0x53
#define WM_USERCHANGED                  0x0054
#define LANG_LAO                        0x54
#define WM_NOTIFYFORMAT                 0x0055
#define LANG_GALICIAN                   0x56
#define LANG_KONKANI                    0x57
#define LANG_MANIPURI                   0x58
#define LANG_SINDHI                     0x59
#define LANG_SYRIAC                     0x5a
#define VK_LWIN                         0x5B
#define LANG_SINHALESE                  0x5b
#define VK_RWIN                         0x5C
#define LANG_CHEROKEE                   0x5c
#define VK_APPS                         0x5D
#define LANG_INUKTITUT                  0x5d
#define LANG_AMHARIC                    0x5e
#define VK_SLEEP                        0x5F
#define LANG_TAMAZIGHT                  0x5f
#define VK_NUMPAD0                      0x60
#define LANG_KASHMIRI                   0x60
#define VK_NUMPAD1                      0x61
#define LANG_NEPALI                     0x61
#define VK_NUMPAD2                      0x62
#define LANG_FRISIAN                    0x62
#define VK_NUMPAD3                      0x63
#define LANG_PASHTO                     0x63
#define WINAPI_FAMILY_DESKTOP_APP       100
#define VK_NUMPAD4                      0x64
#define LANG_FILIPINO                   0x64
#define VS_USER_DEFINED                 100
#define VK_NUMPAD5                      0x65
#define LANG_DIVEHI                     0x65
#define VK_NUMPAD6                      0x66
#define VK_NUMPAD7                      0x67
#define LANG_FULAH                      0x67
#define LANG_PULAR                      0x67
#define VK_NUMPAD8                      0x68
#define LANG_HAUSA                      0x68
#define VK_NUMPAD9                      0x69
#define VK_MULTIPLY                     0x6A
#define LANG_YORUBA                     0x6a
#define VK_ADD                          0x6B
#define LANG_QUECHUA                    0x6b
#define VK_SEPARATOR                    0x6C
#define LANG_SOTHO                      0x6c
#define VK_SUBTRACT                     0x6D
#define LANG_BASHKIR                    0x6d
#define VK_DECIMAL                      0x6E
#define LANG_LUXEMBOURGISH              0x6e
#define VK_DIVIDE                       0x6F
#define LANG_GREENLANDIC                0x6f
#define VK_F1                           0x70
#define LANG_IGBO                       0x70
#define VK_F2                           0x71
#define VK_F3                           0x72
#define VK_F4                           0x73
#define LANG_TIGRIGNA                   0x73
#define LANG_TIGRINYA                   0x73
#define VK_F5                           0x74
#define VK_F6                           0x75
#define LANG_HAWAIIAN                   0x75
#define VK_F7                           0x76
#define VK_F8                           0x77
#define VK_F9                           0x78
#define WHEEL_DELTA                     120
#define LANG_YI                         0x78
#define VK_F10                          0x79
#define VK_F11                          0x7A
#define LANG_MAPUDUNGUN                 0x7a
#define VK_F12                          0x7B
#define WM_CONTEXTMENU                  0x007B
#define VK_F13                          0x7C
#define WM_STYLECHANGING                0x007C
#define LANG_MOHAWK                     0x7c
#define VK_F14                          0x7D
#define WM_STYLECHANGED                 0x007D
#define VK_F15                          0x7E
#define WM_DISPLAYCHANGE                0x007E
#define LANG_BRETON                     0x7e
#define VK_F16                          0x7F
#define WM_GETICON                      0x007F
#define LANG_INVARIANT                  0x7f
#define VK_F17                          0x80
#define WM_SETICON                      0x0080
#define WVR_ALIGNRIGHT                  0x0080
#define CS_PARENTDC                     0x0080
#define CF_OWNERDISPLAY                 0x0080
#define HDS_FULLDRAG                    0x0080
#define BTNS_WHOLEDROPDOWN              0x0080
#define TTS_CLOSE                       0x80
#define TBS_NOTHUMB                     0x0080
#define UDS_NOTHOUSANDS                 0x0080
#define LVS_NOLABELWRAP                 0x0080
#define TVS_NOTOOLTIPS                  0x0080
#define TVS_EX_PARTIALCHECKBOXES        0x0080
#define TCS_VERTICAL                    0x0080
#define MCS_SHORTDAYSOFWEEK             0x0080
#define LANG_UIGHUR                     0x80
#define VK_F18                          0x81
#define WM_NCCREATE                     0x0081
#define CF_DSPTEXT                      0x0081
#define LANG_MAORI                      0x81
#define VK_F19                          0x82
#define WM_NCDESTROY                    0x0082
#define CF_DSPBITMAP                    0x0082
#define LANG_OCCITAN                    0x82
#define VK_F20                          0x83
#define WM_NCCALCSIZE                   0x0083
#define CF_DSPMETAFILEPICT              0x0083
#define LANG_CORSICAN                   0x83
#define VK_F21                          0x84
#define WM_NCHITTEST                    0x0084
#define LANG_ALSATIAN                   0x84
#define VK_F22                          0x85
#define WM_NCPAINT                      0x0085
#define LANG_SAKHA                      0x85
#define LANG_YAKUT                      0x85
#define VK_F23                          0x86
#define WM_NCACTIVATE                   0x0086
#define LANG_KICHE                      0x86
#define VK_F24                          0x87
#define WM_GETDLGCODE                   0x0087
#define LANG_KINYARWANDA                0x87
#define VK_NAVIGATION_VIEW              0x88
#define WM_SYNCPAINT                    0x0088
#define LANG_WOLOF                      0x88
#define VK_NAVIGATION_MENU              0x89
#define VK_NAVIGATION_UP                0x8A
#define VK_NAVIGATION_DOWN              0x8B
#define VK_NAVIGATION_LEFT              0x8C
#define LANG_DARI                       0x8c
#define VK_NAVIGATION_RIGHT             0x8D
#define VK_NAVIGATION_ACCEPT            0x8E
#define CF_DSPENHMETAFILE               0x008E
#define VK_NAVIGATION_CANCEL            0x8F
#define VK_NUMLOCK                      0x90
#define VK_SCROLL                       0x91
#define LANG_SCOTTISH_GAELIC            0x91
#define VK_OEM_NEC_EQUAL                0x92
#define VK_OEM_FJ_JISHO                 0x92
#define LANG_CENTRAL_KURDISH            0x92
#define VK_OEM_FJ_MASSHOU               0x93
#define VK_OEM_FJ_TOUROKU               0x94
#define VK_OEM_FJ_LOYA                  0x95
#define VK_OEM_FJ_ROYA                  0x96
#define VK_LSHIFT                       0xA0
#define WM_NCMOUSEMOVE                  0x00A0
#define VK_RSHIFT                       0xA1
#define WM_NCLBUTTONDOWN                0x00A1
#define VK_LCONTROL                     0xA2
#define WM_NCLBUTTONUP                  0x00A2
#define VK_RCONTROL                     0xA3
#define WM_NCLBUTTONDBLCLK              0x00A3
#define VK_LMENU                        0xA4
#define WM_NCRBUTTONDOWN                0x00A4
#define VK_RMENU                        0xA5
#define WM_NCRBUTTONUP                  0x00A5
#define VK_BROWSER_BACK                 0xA6
#define WM_NCRBUTTONDBLCLK              0x00A6
#define VK_BROWSER_FORWARD              0xA7
#define WM_NCMBUTTONDOWN                0x00A7
#define VK_BROWSER_REFRESH              0xA8
#define WM_NCMBUTTONUP                  0x00A8
#define VK_BROWSER_STOP                 0xA9
#define WM_NCMBUTTONDBLCLK              0x00A9
#define VK_BROWSER_SEARCH               0xAA
#define VK_BROWSER_FAVORITES            0xAB
#define WM_NCXBUTTONDOWN                0x00AB
#define VK_BROWSER_HOME                 0xAC
#define WM_NCXBUTTONUP                  0x00AC
#define VK_VOLUME_MUTE                  0xAD
#define WM_NCXBUTTONDBLCLK              0x00AD
#define VK_VOLUME_DOWN                  0xAE
#define VK_VOLUME_UP                    0xAF
#define VK_MEDIA_NEXT_TRACK             0xB0
#define EM_GETSEL                       0x00B0
#define VK_MEDIA_PREV_TRACK             0xB1
#define EM_SETSEL                       0x00B1
#define VK_MEDIA_STOP                   0xB2
#define EM_GETRECT                      0x00B2
#define VK_MEDIA_PLAY_PAUSE             0xB3
#define EM_SETRECT                      0x00B3
#define VK_LAUNCH_MAIL                  0xB4
#define EM_SETRECTNP                    0x00B4
#define VK_LAUNCH_MEDIA_SELECT          0xB5
#define EM_SCROLL                       0x00B5
#define VK_LAUNCH_APP1                  0xB6
#define EM_LINESCROLL                   0x00B6
#define VK_LAUNCH_APP2                  0xB7
#define EM_SCROLLCARET                  0x00B7
#define EM_GETMODIFY                    0x00B8
#define EM_SETMODIFY                    0x00B9
#define VK_OEM_1                        0xBA
#define EM_GETLINECOUNT                 0x00BA
#define VK_OEM_PLUS                     0xBB
#define EM_LINEINDEX                    0x00BB
#define VK_OEM_COMMA                    0xBC
#define EM_SETHANDLE                    0x00BC
#define VK_OEM_MINUS                    0xBD
#define EM_GETHANDLE                    0x00BD
#define VK_OEM_PERIOD                   0xBE
#define EM_GETTHUMB                     0x00BE
#define VK_OEM_2                        0xBF
#define VK_OEM_3                        0xC0
#define EM_LINELENGTH                   0x00C1
#define EM_REPLACESEL                   0x00C2
#define VK_GAMEPAD_A                    0xC3
#define VK_GAMEPAD_B                    0xC4
#define EM_GETLINE                      0x00C4
#define VK_GAMEPAD_X                    0xC5
#define EM_LIMITTEXT                    0x00C5
#define VK_GAMEPAD_Y                    0xC6
#define EM_CANUNDO                      0x00C6
#define VK_GAMEPAD_RIGHT_SHOULDER       0xC7
#define EM_UNDO                         0x00C7
#define VK_GAMEPAD_LEFT_SHOULDER        0xC8
#define EM_FMTLINES                     0x00C8
#define VK_GAMEPAD_LEFT_TRIGGER         0xC9
#define EM_LINEFROMCHAR                 0x00C9
#define VK_GAMEPAD_RIGHT_TRIGGER        0xCA
#define VK_GAMEPAD_DPAD_UP              0xCB
#define EM_SETTABSTOPS                  0x00CB
#define VK_GAMEPAD_DPAD_DOWN            0xCC
#define EM_SETPASSWORDCHAR              0x00CC
#define VK_GAMEPAD_DPAD_LEFT            0xCD
#define EM_EMPTYUNDOBUFFER              0x00CD
#define VK_GAMEPAD_DPAD_RIGHT           0xCE
#define EM_GETFIRSTVISIBLELINE          0x00CE
#define VK_GAMEPAD_MENU                 0xCF
#define EM_SETREADONLY                  0x00CF
#define VK_GAMEPAD_VIEW                 0xD0
#define EM_SETWORDBREAKPROC             0x00D0
#define VK_GAMEPAD_LEFT_THUMBSTICK_BUTTON 0xD1
#define EM_GETWORDBREAKPROC             0x00D1
#define VK_GAMEPAD_RIGHT_THUMBSTICK_BUTTON 0xD2
#define EM_GETPASSWORDCHAR              0x00D2
#define VK_GAMEPAD_LEFT_THUMBSTICK_UP   0xD3
#define EM_SETMARGINS                   0x00D3
#define VK_GAMEPAD_LEFT_THUMBSTICK_DOWN 0xD4
#define EM_GETMARGINS                   0x00D4
#define VK_GAMEPAD_LEFT_THUMBSTICK_RIGHT 0xD5
#define EM_GETLIMITTEXT                 0x00D5
#define VK_GAMEPAD_LEFT_THUMBSTICK_LEFT 0xD6
#define EM_POSFROMCHAR                  0x00D6
#define VK_GAMEPAD_RIGHT_THUMBSTICK_UP  0xD7
#define EM_CHARFROMPOS                  0x00D7
#define VK_GAMEPAD_RIGHT_THUMBSTICK_DOWN 0xD8
#define EM_SETIMESTATUS                 0x00D8
#define VK_GAMEPAD_RIGHT_THUMBSTICK_RIGHT 0xD9
#define EM_GETIMESTATUS                 0x00D9
#define VK_GAMEPAD_RIGHT_THUMBSTICK_LEFT 0xDA
#define EM_ENABLEFEATURE                0x00DA
#define VK_OEM_4                        0xDB
#define VK_OEM_5                        0xDC
#define VK_OEM_6                        0xDD
#define VK_OEM_7                        0xDE
#define VK_OEM_8                        0xDF
#define VK_OEM_AX                       0xE1
#define VK_OEM_102                      0xE2
#define VK_ICO_HELP                     0xE3
#define VK_ICO_00                       0xE4
#define VK_PROCESSKEY                   0xE5
#define VK_ICO_CLEAR                    0xE6
#define VK_PACKET                       0xE7
#define VK_OEM_RESET                    0xE9
#define VK_OEM_JUMP                     0xEA
#define VK_OEM_PA1                      0xEB
#define VK_OEM_PA2                      0xEC
#define VK_OEM_PA3                      0xED
#define VK_OEM_WSCTRL                   0xEE
#define VK_OEM_CUSEL                    0xEF
#define VK_OEM_ATTN                     0xF0
#define BM_GETCHECK                     0x00F0
#define VK_OEM_FINISH                   0xF1
#define BM_SETCHECK                     0x00F1
#define VK_OEM_COPY                     0xF2
#define BM_GETSTATE                     0x00F2
#define VK_OEM_AUTO                     0xF3
#define BM_SETSTATE                     0x00F3
#define VK_OEM_ENLW                     0xF4
#define BM_SETSTYLE                     0x00F4
#define VK_OEM_BACKTAB                  0xF5
#define BM_CLICK                        0x00F5
#define VK_ATTN                         0xF6
#define BM_GETIMAGE                     0x00F6
#define VK_CRSEL                        0xF7
#define BM_SETIMAGE                     0x00F7
#define VK_EXSEL                        0xF8
#define BM_SETDONTCLICK                 0x00F8
#define VK_EREOF                        0xF9
#define VK_PLAY                         0xFA
#define VK_ZOOM                         0xFB
#define VK_NONAME                       0xFC
#define VK_PA1                          0xFD
#define VK_OEM_CLEAR                    0xFE
#define WM_INPUT_DEVICE_CHANGE          0x00FE
#define SUBVERSION_MASK                 0x000000FF
#define WM_INPUT                        0x00FF
#define WM_KEYFIRST                     0x0100
#define WM_KEYDOWN                      0x0100
#define WVR_HREDRAW                     0x0100
#define HDS_FILTERBAR                   0x0100
#define TBSTYLE_TOOLTIPS                0x0100
#define RBS_TOOLTIPS                    0x00000100
#define TTS_USEVISUALSTYLE              0x100
#define SBARS_SIZEGRIP                  0x0100
#define TBS_TOOLTIPS                    0x0100
#define UDS_HOTTRACK                    0x0100
#define LVS_AUTOARRANGE                 0x0100
#define TVS_CHECKBOXES                  0x0100
#define TVS_EX_EXCLUSIONCHECKBOXES      0x0100
#define TCS_BUTTONS                     0x0100
#define MCS_NOSELCHANGEONNAV            0x0100
#define WM_KEYUP                        0x0101
#define WM_CHAR                         0x0102
#define WM_DEADCHAR                     0x0103
#define WM_SYSKEYDOWN                   0x0104
#define WM_SYSKEYUP                     0x0105
#define WM_SYSCHAR                      0x0106
#define WM_SYSDEADCHAR                  0x0107
#define WM_UNICHAR                      0x0109
#define WM_KEYLAST                      0x0109
#define WM_IME_STARTCOMPOSITION         0x010D
#define WM_IME_ENDCOMPOSITION           0x010E
#define WM_IME_COMPOSITION              0x010F
#define WM_IME_KEYLAST                  0x010F
#define WM_INITDIALOG                   0x0110
#define WM_COMMAND                      0x0111
#define WM_SYSCOMMAND                   0x0112
#define WM_TIMER                        0x0113
#define WM_HSCROLL                      0x0114
#define WM_VSCROLL                      0x0115
#define WM_INITMENU                     0x0116
#define WM_INITMENUPOPUP                0x0117
#define WM_GESTURE                      0x0119
#define WM_GESTURENOTIFY                0x011A
#define WM_MENUSELECT                   0x011F
#define WM_MENUCHAR                     0x0120
#define WM_ENTERIDLE                    0x0121
#define WM_MENURBUTTONUP                0x0122
#define WM_MENUDRAG                     0x0123
#define WM_MENUGETOBJECT                0x0124
#define WM_UNINITMENUPOPUP              0x0125
#define WM_MENUCOMMAND                  0x0126
#define WM_CHANGEUISTATE                0x0127
#define WM_UPDATEUISTATE                0x0128
#define WM_QUERYUISTATE                 0x0129
#define WM_CTLCOLORMSGBOX               0x0132
#define WM_CTLCOLOREDIT                 0x0133
#define WM_CTLCOLORLISTBOX              0x0134
#define WM_CTLCOLORBTN                  0x0135
#define WM_CTLCOLORDLG                  0x0136
#define WM_CTLCOLORSCROLLBAR            0x0137
#define WM_CTLCOLORSTATIC               0x0138
#define MN_GETHMENU                     0x01E1
#define _WIN32_IE_IE20                  0x0200
#define WM_MOUSEFIRST                   0x0200
#define WM_MOUSEMOVE                    0x0200
#define WVR_VREDRAW                     0x0200
#define CS_NOCLOSE                      0x0200
#define CF_PRIVATEFIRST                 0x0200
#define HDS_FLAT                        0x0200
#define TBSTYLE_WRAPABLE                0x0200
#define RBS_VARHEIGHT                   0x00000200
#define TBS_REVERSED                    0x0200
#define LVS_EDITLABELS                  0x0200
#define TVS_TRACKSELECT                 0x0200
#define TVS_EX_DIMMEDCHECKBOXES         0x0200
#define TCS_MULTILINE                   0x0200
#define WM_LBUTTONDOWN                  0x0201
#define WM_LBUTTONUP                    0x0202
#define WM_LBUTTONDBLCLK                0x0203
#define WM_RBUTTONDOWN                  0x0204
#define WM_RBUTTONUP                    0x0205
#define WM_RBUTTONDBLCLK                0x0206
#define WM_MBUTTONDOWN                  0x0207
#define WM_MBUTTONUP                    0x0208
#define WM_MBUTTONDBLCLK                0x0209
#define WM_MOUSEWHEEL                   0x020A
#define WM_XBUTTONDOWN                  0x020B
#define WM_XBUTTONUP                    0x020C
#define WM_XBUTTONDBLCLK                0x020D
#define WM_MOUSEHWHEEL                  0x020E
#define WM_MOUSELAST                    0x020E
#define WM_PARENTNOTIFY                 0x0210
#define WM_ENTERMENULOOP                0x0211
#define WM_EXITMENULOOP                 0x0212
#define WM_NEXTMENU                     0x0213
#define WM_SIZING                       0x0214
#define WM_CAPTURECHANGED               0x0215
#define WM_MOVING                       0x0216
#define WM_POWERBROADCAST               0x0218
#define WM_DEVICECHANGE                 0x0219
#define WM_MDICREATE                    0x0220
#define WM_MDIDESTROY                   0x0221
#define WM_MDIACTIVATE                  0x0222
#define WM_MDIRESTORE                   0x0223
#define WM_MDINEXT                      0x0224
#define WM_MDIMAXIMIZE                  0x0225
#define WM_MDITILE                      0x0226
#define WM_MDICASCADE                   0x0227
#define WM_MDIICONARRANGE               0x0228
#define WM_MDIGETACTIVE                 0x0229
#define WM_MDISETMENU                   0x0230
#define WM_ENTERSIZEMOVE                0x0231
#define WM_EXITSIZEMOVE                 0x0232
#define WM_DROPFILES                    0x0233
#define WM_MDIREFRESHMENU               0x0234
#define WM_POINTERDEVICECHANGE          0x238
#define WM_POINTERDEVICEINRANGE         0x239
#define WM_POINTERDEVICEOUTOFRANGE      0x23A
#define WM_TOUCH                        0x0240
#define WM_NCPOINTERUPDATE              0x0241
#define WM_NCPOINTERDOWN                0x0242
#define WM_NCPOINTERUP                  0x0243
#define WM_POINTERUPDATE                0x0245
#define WM_POINTERDOWN                  0x0246
#define WM_POINTERUP                    0x0247
#define WM_POINTERENTER                 0x0249
#define WM_POINTERLEAVE                 0x024A
#define WM_POINTERACTIVATE              0x024B
#define WM_POINTERCAPTURECHANGED        0x024C
#define WM_TOUCHHITTESTING              0x024D
#define WM_POINTERWHEEL                 0x024E
#define WM_POINTERHWHEEL                0x024F
#define DM_POINTERHITTEST               0x0250
#define WM_POINTERROUTEDTO              0x0251
#define WM_POINTERROUTEDAWAY            0x0252
#define WM_POINTERROUTEDRELEASED        0x0253
#define WM_IME_SETCONTEXT               0x0281
#define WM_IME_NOTIFY                   0x0282
#define WM_IME_CONTROL                  0x0283
#define WM_IME_COMPOSITIONFULL          0x0284
#define WM_IME_SELECT                   0x0285
#define WM_IME_CHAR                     0x0286
#define WM_IME_REQUEST                  0x0288
#define WM_IME_KEYDOWN                  0x0290
#define WM_IME_KEYUP                    0x0291
#define WM_NCMOUSEHOVER                 0x02A0
#define WM_MOUSEHOVER                   0x02A1
#define WM_NCMOUSELEAVE                 0x02A2
#define WM_MOUSELEAVE                   0x02A3
#define WM_WTSSESSION_CHANGE            0x02B1
#define WM_TABLET_FIRST                 0x02c0
#define WM_TABLET_LAST                  0x02df
#define WM_DPICHANGED                   0x02E0
#define WM_DPICHANGED_BEFOREPARENT      0x02E2
#define WM_DPICHANGED_AFTERPARENT       0x02E3
#define WM_GETDPISCALEDSIZE             0x02E4
#define CF_PRIVATELAST                  0x02FF
#define _WIN32_IE_IE30                  0x0300
#define WM_CUT                          0x0300
#define CF_GDIOBJFIRST                  0x0300
#define WM_COPY                         0x0301
#define _WIN32_IE_IE302                 0x0302
#define WM_PASTE                        0x0302
#define WM_CLEAR                        0x0303
#define WM_UNDO                         0x0304
#define WM_RENDERFORMAT                 0x0305
#define WM_RENDERALLFORMATS             0x0306
#define WM_DESTROYCLIPBOARD             0x0307
#define WM_DRAWCLIPBOARD                0x0308
#define WM_PAINTCLIPBOARD               0x0309
#define WM_VSCROLLCLIPBOARD             0x030A
#define WM_SIZECLIPBOARD                0x030B
#define WM_ASKCBFORMATNAME              0x030C
#define WM_CHANGECBCHAIN                0x030D
#define WM_HSCROLLCLIPBOARD             0x030E
#define WM_QUERYNEWPALETTE              0x030F
#define WM_PALETTEISCHANGING            0x0310
#define WM_PALETTECHANGED               0x0311
#define WM_HOTKEY                       0x0312
#define WM_PRINT                        0x0317
#define WM_PRINTCLIENT                  0x0318
#define WM_APPCOMMAND                   0x0319
#define WM_THEMECHANGED                 0x031A
#define WM_CLIPBOARDUPDATE              0x031D
#define WM_DWMCOMPOSITIONCHANGED        0x031E
#define WM_DWMNCRENDERINGCHANGED        0x031F
#define WM_DWMCOLORIZATIONCOLORCHANGED  0x0320
#define WM_DWMWINDOWMAXIMIZEDCHANGE     0x0321
#define WM_DWMSENDICONICTHUMBNAIL       0x0323
#define WM_DWMSENDICONICLIVEPREVIEWBITMAP 0x0326
#define WM_GETTITLEBARINFOEX            0x033F
#define WM_HANDHELDFIRST                0x0358
#define WM_HANDHELDLAST                 0x035F
#define WM_AFXFIRST                     0x0360
#define WM_AFXLAST                      0x037F
#define WM_PENWINFIRST                  0x0380
#define WM_PENWINLAST                   0x038F
#define WM_DDE_FIRST                    0x03E0
#define CF_GDIOBJLAST                   0x03FF
#define _WIN32_WINNT_NT4                0x0400
#define _WIN32_IE_IE40                  0x0400
#define WM_USER                         0x0400
#define WVR_VALIDRECTS                  0x0400
#define HDS_CHECKBOXES                  0x0400
#define TBSTYLE_ALTDRAG                 0x0400
#define RBS_BANDBORDERS                 0x00000400
#define TBS_DOWNISLEFT                  0x0400
#define LVS_OWNERDRAWFIXED              0x0400
#define TVS_SINGLEEXPAND                0x0400
#define TVS_EX_DRAWIMAGEASYNC           0x0400
#define TCS_FIXEDWIDTH                  0x0400
#define ctlFirst                        0x0400
#define psh1                            0x0400
#define _WIN32_IE_IE401                 0x0401
#define psh2                            0x0401
#define psh3                            0x0402
#define psh4                            0x0403
#define psh5                            0x0404
#define psh6                            0x0405
#define psh7                            0x0406
#define psh8                            0x0407
#define psh9                            0x0408
#define psh10                           0x0409
#define psh11                           0x040a
#define psh12                           0x040b
#define psh13                           0x040c
#define psh14                           0x040d
#define psh15                           0x040e
#define psh16                           0x040f
#define _WIN32_WINDOWS                  0x0410
#define chx1                            0x0410
#define chx2                            0x0411
#define chx3                            0x0412
#define chx4                            0x0413
#define chx5                            0x0414
#define chx6                            0x0415
#define chx7                            0x0416
#define chx8                            0x0417
#define chx9                            0x0418
#define chx10                           0x0419
#define chx11                           0x041a
#define chx12                           0x041b
#define chx13                           0x041c
#define chx14                           0x041d
#define chx15                           0x041e
#define chx16                           0x041f
#define rad1                            0x0420
#define rad2                            0x0421
#define rad3                            0x0422
#define rad4                            0x0423
#define rad5                            0x0424
#define rad6                            0x0425
#define rad7                            0x0426
#define rad8                            0x0427
#define rad9                            0x0428
#define rad10                           0x0429
#define rad11                           0x042a
#define rad12                           0x042b
#define rad13                           0x042c
#define rad14                           0x042d
#define rad15                           0x042e
#define rad16                           0x042f
#define grp1                            0x0430
#define grp2                            0x0431
#define grp3                            0x0432
#define grp4                            0x0433
#define frm1                            0x0434
#define frm2                            0x0435
#define frm3                            0x0436
#define frm4                            0x0437
#define rct1                            0x0438
#define rct2                            0x0439
#define rct3                            0x043a
#define rct4                            0x043b
#define ico1                            0x043c
#define ico2                            0x043d
#define ico3                            0x043e
#define ico4                            0x043f
#define stc1                            0x0440
#define stc2                            0x0441
#define stc3                            0x0442
#define stc4                            0x0443
#define stc5                            0x0444
#define stc6                            0x0445
#define stc7                            0x0446
#define stc8                            0x0447
#define stc9                            0x0448
#define stc10                           0x0449
#define stc11                           0x044a
#define stc12                           0x044b
#define stc13                           0x044c
#define stc14                           0x044d
#define stc15                           0x044e
#define stc16                           0x044f
#define stc17                           0x0450
#define stc18                           0x0451
#define stc19                           0x0452
#define stc20                           0x0453
#define stc21                           0x0454
#define stc22                           0x0455
#define stc23                           0x0456
#define stc24                           0x0457
#define stc25                           0x0458
#define stc26                           0x0459
#define stc27                           0x045a
#define stc28                           0x045b
#define stc29                           0x045c
#define stc30                           0x045d
#define stc31                           0x045e
#define stc32                           0x045f
#define lst1                            0x0460
#define lst2                            0x0461
#define lst3                            0x0462
#define lst4                            0x0463
#define lst5                            0x0464
#define lst6                            0x0465
#define lst7                            0x0466
#define lst8                            0x0467
#define lst9                            0x0468
#define lst10                           0x0469
#define lst11                           0x046a
#define lst12                           0x046b
#define lst13                           0x046c
#define lst14                           0x046d
#define lst15                           0x046e
#define lst16                           0x046f
#define cmb1                            0x0470
#define cmb2                            0x0471
#define cmb3                            0x0472
#define cmb4                            0x0473
#define cmb5                            0x0474
#define cmb6                            0x0475
#define cmb7                            0x0476
#define cmb8                            0x0477
#define cmb9                            0x0478
#define cmb10                           0x0479
#define cmb11                           0x047a
#define cmb12                           0x047b
#define cmb13                           0x047c
#define cmb14                           0x047d
#define cmb15                           0x047e
#define cmb16                           0x047f
#define edt1                            0x0480
#define edt2                            0x0481
#define edt3                            0x0482
#define edt4                            0x0483
#define edt5                            0x0484
#define edt6                            0x0485
#define edt7                            0x0486
#define edt8                            0x0487
#define edt9                            0x0488
#define edt10                           0x0489
#define edt11                           0x048a
#define edt12                           0x048b
#define edt13                           0x048c
#define edt14                           0x048d
#define edt15                           0x048e
#define edt16                           0x048f
#define scr1                            0x0490
#define scr2                            0x0491
#define scr3                            0x0492
#define scr4                            0x0493
#define scr5                            0x0494
#define scr6                            0x0495
#define scr7                            0x0496
#define scr8                            0x0497
#define ctl1                            0x04A0
#define ctlLast                         0x04ff
#define _WIN32_WINNT_WIN2K              0x0500
#define _WIN32_IE_IE50                  0x0500
#define _WIN32_WINNT_WINXP              0x0501
#define _WIN32_IE_IE501                 0x0501
#define _WIN32_WINNT_WS03               0x0502
#define _WIN32_IE_IE55                  0x0550
#define _WIN32_WINNT_WIN6               0x0600
#define _WIN32_WINNT_VISTA              0x0600
#define _WIN32_WINNT_WS08               0x0600
#define _WIN32_WINNT_LONGHORN           0x0600
#define _WIN32_IE_IE60                  0x0600
#define FILEOPENORD                     1536
#define _WIN32_WINNT_WIN7               0x0601
#define _WIN32_IE_IE60SP1               0x0601
#define MULTIFILEOPENORD                1537
#define _WIN32_WINNT_WIN8               0x0602
#define _WIN32_IE_WS03                  0x0602
#define PRINTDLGORD                     1538
#define _WIN32_WINNT_WINBLUE            0x0603
#define _WIN32_IE_IE60SP2               0x0603
#define PRNSETUPDLGORD                  1539
#define FINDDLGORD                      1540
#define REPLACEDLGORD                   1541
#define FONTDLGORD                      1542
#define FORMATDLGORD31                  1543
#define FORMATDLGORD30                  1544
#define RUNDLGORD                       1545
#define PAGESETUPDLGORD                 1546
#define NEWFILEOPENORD                  1547
#define PRINTDLGEXORD                   1549
#define PAGESETUPDLGORDMOTIF            1550
#define COLORMGMTDLGORD                 1551
#define NEWFILEOPENV2ORD                1552
#define NEWFILEOPENV3ORD                1553
#define NEWFORMATDLGWITHLINK            1591
#define IDC_MANAGE_LINK                 1592
#define _WIN32_IE_IE70                  0x0700
#define _WIN32_IE_IE80                  0x0800
#define CS_SAVEBITS                     0x0800
#define HDS_NOSIZING                    0x0800
#define TBSTYLE_FLAT                    0x0800
#define RBS_FIXEDORDER                  0x00000800
#define SBARS_TOOLTIPS                  0x0800
#define SBT_TOOLTIPS                    0x0800
#define TBS_NOTIFYBEFOREMOVE            0x0800
#define LVS_ALIGNLEFT                   0x0800
#define TVS_INFOTIP                     0x0800
#define TCS_RAGGEDRIGHT                 0x0800
#define _WIN32_IE_IE90                  0x0900
#define _WIN32_WINNT_WINTHRESHOLD       0x0A00
#define _WIN32_WINNT_WIN10              0x0A00
#define _WIN32_IE_IE100                 0x0A00
#define _WIN32_IE_IE110                 0x0A00
#define _WIN32_WINNT                    0x0A00
#define _WIN32_IE                       0x0A00
#define LVS_ALIGNMASK                   0x0c00
#define CS_BYTEALIGNCLIENT              0x1000
#define HDS_OVERFLOW                    0x1000
#define TBSTYLE_LIST                    0x1000
#define RBS_REGISTERDROP                0x00001000
#define TBS_TRANSPARENTBKGND            0x1000
#define LVS_OWNERDATA                   0x1000
#define TVS_FULLROWSELECT               0x1000
#define TCS_FOCUSONBUTTONDOWN           0x1000
#define CS_BYTEALIGNWINDOW              0x2000
#define TBSTYLE_CUSTOMERASE             0x2000
#define RBS_AUTOSIZE                    0x00002000
#define LVS_NOSCROLL                    0x2000
#define TVS_NOSCROLL                    0x2000
#define TCS_OWNERDRAWFIXED              0x2000
#define CS_GLOBALCLASS                  0x4000
#define TBSTYLE_REGISTERDROP            0x4000
#define RBS_VERTICALGRIPPER             0x00004000
#define LVS_NOCOLUMNHEADER              0x4000
#define TVS_NONEVENHEIGHT               0x4000
#define TCS_TOOLTIPS                    0x4000
#define IDH_NO_HELP                     28440
#define IDH_MISSING_CONTEXT             28441
#define IDH_GENERIC_HELP_BUTTON         28442
#define IDH_OK                          28443
#define IDH_CANCEL                      28444
#define IDH_HELP                        28445
#define LANG_BOSNIAN_NEUTRAL            0x781a
#define LANG_CHINESE_TRADITIONAL        0x7c04
#define LANG_SERBIAN_NEUTRAL            0x7c1a
#define IDTIMEOUT                       32000
#define OCR_NORMAL                      32512
#define OIC_SAMPLE                      32512
#define IDI_APPLICATION                 32512
#define OCR_IBEAM                       32513
#define OIC_HAND                        32513
#define IDI_HAND                        32513
#define OCR_WAIT                        32514
#define OIC_QUES                        32514
#define IDI_QUESTION                    32514
#define OCR_CROSS                       32515
#define OIC_BANG                        32515
#define IDI_EXCLAMATION                 32515
#define OCR_UP                          32516
#define OIC_NOTE                        32516
#define IDI_ASTERISK                    32516
#define OIC_WINLOGO                     32517
#define IDI_WINLOGO                     32517
#define OIC_SHIELD                      32518
#define IDI_SHIELD                      32518
#define OCR_SIZE                        32640
#define OCR_ICON                        32641
#define OCR_SIZENWSE                    32642
#define OCR_SIZENESW                    32643
#define OCR_SIZEWE                      32644
#define OCR_SIZENS                      32645
#define OCR_SIZEALL                     32646
#define OCR_ICOCUR                      32647
#define OCR_NO                          32648
#define OCR_HAND                        32649
#define OCR_APPSTARTING                 32650
#define OBM_LFARROWI                    32734
#define OBM_RGARROWI                    32735
#define OBM_DNARROWI                    32736
#define OBM_UPARROWI                    32737
#define OBM_COMBO                       32738
#define OBM_MNARROW                     32739
#define OBM_LFARROWD                    32740
#define OBM_RGARROWD                    32741
#define OBM_DNARROWD                    32742
#define OBM_UPARROWD                    32743
#define OBM_RESTORED                    32744
#define OBM_ZOOMD                       32745
#define OBM_REDUCED                     32746
#define OBM_RESTORE                     32747
#define OBM_ZOOM                        32748
#define OBM_REDUCE                      32749
#define OBM_LFARROW                     32750
#define OBM_RGARROW                     32751
#define OBM_DNARROW                     32752
#define OBM_UPARROW                     32753
#define OBM_CLOSE                       32754
#define OBM_OLD_RESTORE                 32755
#define OBM_OLD_ZOOM                    32756
#define OBM_OLD_REDUCE                  32757
#define OBM_BTNCORNERS                  32758
#define OBM_CHECKBOXES                  32759
#define OBM_CHECK                       32760
#define OBM_BTSIZE                      32761
#define OBM_OLD_LFARROW                 32762
#define OBM_OLD_RGARROW                 32763
#define OBM_OLD_DNARROW                 32764
#define OBM_OLD_UPARROW                 32765
#define OBM_SIZE                        32766
#define OBM_OLD_CLOSE                   32767
#define WM_APP                          0x8000
#define HELP_TCARD                      0x8000
#define TBSTYLE_TRANSPARENT             0x8000
#define RBS_DBLCLKTOGGLE                0x00008000
#define LVS_NOSORTHEADER                0x8000
#define TVS_NOHSCROLL                   0x8000
#define TCS_FOCUSNEVER                  0x8000
#define SC_SIZE                         0xF000
#define SC_SEPARATOR                    0xF00F
#define SC_MOVE                         0xF010
#define SC_MINIMIZE                     0xF020
#define SC_MAXIMIZE                     0xF030
#define SC_NEXTWINDOW                   0xF040
#define SC_PREVWINDOW                   0xF050
#define SC_CLOSE                        0xF060
#define SC_VSCROLL                      0xF070
#define SC_HSCROLL                      0xF080
#define SC_MOUSEMENU                    0xF090
#define SC_KEYMENU                      0xF100
#define SC_ARRANGE                      0xF110
#define SC_RESTORE                      0xF120
#define SC_TASKLIST                     0xF130
#define SC_SCREENSAVE                   0xF140
#define SC_HOTKEY                       0xF150
#define SC_DEFAULT                      0xF160
#define SC_MONITORPOWER                 0xF170
#define SC_CONTEXTHELP                  0xF180
#define LVS_TYPESTYLEMASK               0xfc00
#define SPVERSION_MASK                  0x0000FF00
#define HTERROR                         -2
#define PWR_FAIL                        -1
#define UNICODE_NOCHAR                  0xFFFF
#define HTTRANSPARENT                   -1

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        101
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1000
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
