#ifndef CONFWGT_H
#define CONFWGT_H

#include <QLabel>
#include <QTimer>
#include <QWidget>

class AppDataManager;

namespace Ui
{
class ConfWgt;
}

class ConfWgt : public QWidget
{
    Q_OBJECT

  public:
    explicit ConfWgt(QWidget *parent = nullptr);
    ~ConfWgt();

    void setAppDataManager(AppDataManager *appDataManager);
    void loadThemePreference();

  signals:
    void sigChangeSkin(int type, QString path);

  public slots:
    void onThemeSwitchCompleted();

  protected:
    bool eventFilter(QObject *watched, QEvent *event) override;

  private slots:
    void on_btn_theme_clicked();

    void on_btn_about_clicked();

    void on_btn_normal_clicked();

    void on_btn_simple_clicked();

    void on_btn_confirm_clicked();

    void on_btn_close_clicked();

    void showSwitchingStatus();
    void showSwitchCompleteStatus();

  private:
    Ui::ConfWgt *ui;

    QPoint start_pos_;        // 记录鼠标按下时的位置
    bool is_pressed_ = false; // 标记是否正在拖动

    AppDataManager *app_data_manager_ = nullptr;
    QLabel *status_label_ = nullptr; // 状态提示标签
    QTimer *status_timer_ = nullptr; // 状态提示计时器
};

#endif // CONFWGT_H
