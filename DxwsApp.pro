QT       += core gui network widgets

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++23

# 版本信息
VERSION = *******
QMAKE_TARGET_PRODUCT = "德迅工具箱"
QMAKE_TARGET_DESCRIPTION = "集成多种实用工具的桌面应用程序"
QMAKE_TARGET_COMPANY = "浙江德迅网络安全技术有限公司"
QMAKE_TARGET_COPYRIGHT = "Copyright © 2025 浙江德迅网络安全技术有限公司. All rights reserved."


# 强制生成调试信息（即使在Release模式）
CONFIG += force_debug_info

# Windows specific settings
win32 {
    # Windows库
    LIBS += -ladvapi32 -lshell32 -ldbghelp -lpsapi
    # 设置应用程序图标
    RC_ICONS = WindowsAdapter.ico
    
    # Debug配置 - 生成PDB文件和调试信息
    CONFIG(debug, debug|release) {
        # 生成调试信息
        QMAKE_CXXFLAGS_DEBUG += /Zi
        QMAKE_LFLAGS_DEBUG += /DEBUG /INCREMENTAL:NO
        # 确保生成PDB文件
        QMAKE_LFLAGS_DEBUG += /PDB:$$OUT_PWD/debug/$${TARGET}.pdb
    }
    
    # Release配置 - 也生成PDB文件用于崩溃分析
    CONFIG(release, debug|release) {
        # 即使在Release模式下也生成调试信息
        QMAKE_CXXFLAGS_RELEASE += /Zi
        QMAKE_LFLAGS_RELEASE += /DEBUG /OPT:REF /OPT:ICF
        # 生成PDB文件
        QMAKE_LFLAGS_RELEASE += /PDB:$$OUT_PWD/release/$${TARGET}.pdb
    }
    
    # 启用异常处理
    QMAKE_CXXFLAGS += /EHsc
    
    # 定义Windows版本
    DEFINES += WINVER=0x0601 _WIN32_WINNT=0x0601
    
    # 生成映射文件（可选，用于分析）
    QMAKE_LFLAGS += /MAP:$$OUT_PWD/$${TARGET}.map
    
    # 确保符号信息完整
    QMAKE_CXXFLAGS += /FC  # 显示完整路径
    
    QMAKE_LFLAGS += "\"/MANIFESTUAC:level='requireAdministrator' uiAccess='false'\""

}

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# Windows平台专用定义
win32 {
    DEFINES += _CRT_SECURE_NO_WARNINGS
    DEFINES += NOMINMAX
    DEFINES += WIN32_LEAN_AND_MEAN
}

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    basemanager.cpp \
    cardwgt.cpp \
    confwgt.cpp \
    cpflmanager.cpp \
    cyrjmanager.cpp \
    main.cpp \
    dxwswgt.cpp \
    mycommonwgt.cpp \
    opstoolwgt.cpp \
    popmenuwgt.cpp \
    wdgjmanager.cpp \
    ycycmanager.cpp \
    ywgjmanager.cpp \
    appdatamanager.cpp \
    cpflcardwgt.cpp

HEADERS += \
    basemanager.h \
    cardwgt.h \
    confwgt.h \
    cpflmanager.h \
    cyrjmanager.h \
    dxwswgt.h \
    mycommonwgt.h \
    opstoolwgt.h \
    popmenuwgt.h \
    wdgjmanager.h \
    ycycmanager.h \
    ywgjmanager.h \
    appdatamanager.h \
    cpflcardwgt.h

FORMS += \
    cardwgt.ui \
    confwgt.ui \
    dxwswgt.ui \
    mycommonwgt.ui \
    opstoolwgt.ui \
    popmenuwgt.ui \
    cpflcardwgt.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    res.qrc
