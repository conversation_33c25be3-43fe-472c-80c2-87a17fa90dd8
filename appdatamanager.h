#ifndef APPDATAMANAGER_H
#define APPDATAMANAGER_H

#include <QList>
#include <QMap>
#include <QObject>
#include <QSettings>

class AppDataManager : public QObject
{
    Q_OBJECT

  public:
    explicit AppDataManager(QObject *parent = nullptr);

    void loadData();
    void saveData();

    void incrementClickCount(int cardId);
    QList<int> getTopClickedCards(int count = 6) const;

    void setFavoriteLayout(const QList<int> &layout);
    QList<int> getFavoriteLayout() const;

    // 主题偏好设置
    void setThemePreference(int themeType);
    int getThemePreference() const;

  private:
    QMap<int, int> cardClickCounts_;
    QList<int> favoriteLayout_;
    QSettings settings_;

    // Helper function for sorting
    static bool compareCardCounts(const QPair<int, int> &a, const QPair<int, int> &b)
    {
        return a.second > b.second;
    }
};

#endif // APPDATAMANAGER_H