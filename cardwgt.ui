<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CardWgt</class>
 <widget class="QWidget" name="CardWgt">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>155</width>
    <height>54</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>155</width>
    <height>54</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>155</width>
    <height>54</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background:transparent;</string>
  </property>
  <widget class="QFrame" name="frame">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>155</width>
     <height>54</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background: #FFFFFF;
border-radius: 8px;</string>
   </property>
   <property name="frameShape">
    <enum>QFrame::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Raised</enum>
   </property>
   <widget class="QLabel" name="label_icon">
    <property name="geometry">
     <rect>
      <x>12</x>
      <y>10</y>
      <width>34</width>
      <height>34</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>34</width>
      <height>34</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>34</width>
      <height>34</height>
     </size>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="QLabel" name="label_name">
    <property name="geometry">
     <rect>
      <x>59</x>
      <y>20</y>
      <width>81</width>
      <height>16</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #4B5C77;</string>
    </property>
    <property name="text">
     <string>百度网盘</string>
    </property>
   </widget>
   <widget class="QPushButton" name="btn_collect">
    <property name="geometry">
     <rect>
      <x>113</x>
      <y>0</y>
      <width>42</width>
      <height>16</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>42</width>
      <height>15</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton
{
	border-image: url(:/img/collect.png);
	border-radius:0px;
	background:transparent;
}
QPushButton:checked
{
	border-image: url(:/img/collected.png);
}</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
