#ifndef CPFLMANAGER_H
#define CPFLMANAGER_H

#include "cardids.h"
#include "cpflcardwgt.h"
#include <QHBoxLayout>
#include <QList>
#include <QObject>
#include <QVBoxLayout>
#include <QWidget>

// 前向声明
class QMessageBox;

class CpflManager : public QObject
{
    Q_OBJECT

  public:
    explicit CpflManager(QObject *parent = nullptr);
    ~CpflManager();

    // 初始化各个类别的卡片
    void InitYaq();  // 云安全
    void InitYwaq(); // 业务安全
    void InitAqyy(); // 安全运营
    void InitAqfw(); // 安全服务

    // 更新各个类别的布局
    void UpdateYaq(QWidget *container, QWidget *scrollContainer);
    void UpdateYwaq(QWidget *container, QWidget *scrollContainer);
    void UpdateAqyy(QWidget *container, QWidget *scrollContainer);
    void UpdateAqfw(QWidget *container, QWidget *scrollContainer);

    void Init(QWidget *yaq_container, QWidget *yaq_scroll, QWidget *ywaq_container, QWidget *ywaq_scroll,
              QWidget *aqyy_container, QWidget *aqyy_scroll, QWidget *aqfw_container, QWidget *aqfw_scroll);
    // 设置主题
    void SetTheme(int type);

    // 获取卡片列表
    QList<CpflCardWgt *> GetYaqCards() const
    {
        return yaq_cpfl_cards_;
    }
    QList<CpflCardWgt *> GetYwaqCards() const
    {
        return ywaq_cpfl_cards_;
    }
    QList<CpflCardWgt *> GetAqyyCards() const
    {
        return aqyy_cpfl_cards_;
    }
    QList<CpflCardWgt *> GetAqfwCards() const
    {
        return aqfw_cpfl_cards_;
    }

    // 获取所有产品福利卡片
    QList<CpflCardWgt *> GetCpflCards() const;

    // 处理卡片详情点击事件
    void HandleCardDetail(int cardID);

  public slots:
    void OnCardDetailClicked();

  private:
    // 添加卡片的辅助方法
    void AddYaq(const QString &normal_icon, const QString &simple_icon, const QString &name, const QString &content,
                int cardID);
    void AddYwaq(const QString &normal_icon, const QString &simple_icon, const QString &name, const QString &content,
                 int cardID);
    void AddAqyy(const QString &normal_icon, const QString &simple_icon, const QString &name, const QString &content,
                 int cardID);
    void AddAqfw(const QString &normal_icon, const QString &simple_icon, const QString &name, const QString &content,
                 int cardID);

    // 通用的布局更新方法
    void UpdateLayout(QList<CpflCardWgt *> &cards, QWidget *container, QWidget *scrollContainer);

    // 各个产品详情处理子函数
    void ShowDdosDefenseDetail();          // DDoS防御详情
    void ShowSecureCdnDetail();            // 安全加速SCDN详情
    void ShowAntiDShieldDetail();          // 抗D盾详情
    void ShowSslCertificateDetail();       // SSL证书详情
    void ShowAppAccelerationDetail();      // 应用加速详情
    void ShowVulnerabilityScanDetail();    // 漏洞扫描详情
    void ShowGuardDetail();                // 德迅卫士详情
    void ShowCloudMonitoringDetail();      // 云监测详情
    void ShowHiveContainerDetail();        // 德迅蜂巢详情
    void ShowHoneypotDetail();             // 蜜罐详情
    void ShowZeroTrustDetail();            // 德迅零域详情
    void ShowCloudAtlasDetail();           // 德迅云图详情
    void ShowExpertServiceDetail();        // 专家服务详情
    void ShowComplianceConsultingDetail(); // 等保合规咨询详情
    void ShowSituationAwarenessDetail();   // 态势感知详情
    void ShowLogAuditDetail();             // 日志审计详情
    void ShowDatabaseAuditDetail();        // 数据库审计详情
    void ShowPenetrationTestDetail();      // 渗透测试详情
    void ShowCodeAuditDetail();            // 代码审计详情
    void ShowRiskAssessmentDetail();       // 风险评估详情
    void ShowSecurityDrillDetail();        // 信息安全对抗演习详情
    void ShowEmergencyCenterDetail();      // 网络安全应急中心详情

  private:
    QList<CpflCardWgt *> yaq_cpfl_cards_;  // 云安全
    QList<CpflCardWgt *> ywaq_cpfl_cards_; // 业务安全
    QList<CpflCardWgt *> aqyy_cpfl_cards_; // 安全运营
    QList<CpflCardWgt *> aqfw_cpfl_cards_; // 安全服务

    int current_theme_ = 0;           // 当前主题
    bool is_updating_layout_ = false; // 防止递归调用UpdateLayout
};

#endif // CPFLMANAGER_H
