#ifndef CARDIDS_H
#define CARDIDS_H

// 定义卡片ID范围宏
#define CARD_ID_RANGE_WDGJ_START 1001 // 我的工具区域开始
#define CARD_ID_RANGE_WDGJ_END 1199   // 我的工具区域结束
#define CARD_ID_RANGE_YWGJ_START 2001 // 运维工具区域开始
#define CARD_ID_RANGE_YWGJ_END 2099   // 运维工具区域结束
#define CARD_ID_RANGE_YCYC_START 3001 // 压测远程区域开始
#define CARD_ID_RANGE_YCYC_END 3099   // 压测远程区域结束
#define CARD_ID_RANGE_CYRJ_START 4001 // 常用软件区域开始
#define CARD_ID_RANGE_CYRJ_END 4099   // 常用软件区域结束

// 定义卡片ID宏定义
// 我的工具区域 (1001-1099)

// 业务工具区域 (2001-2099)
#define CARD_ID_KMS_ACTIVATE 2001         // KMS激活
#define CARD_ID_WIN_OPTIMIZE 2002         // Win优化
#define CARD_ID_KK_TOOLS 2003             // kk小工具
#define CARD_ID_NET35 2004                // NET3.5
#define CARD_ID_TIME_SYNC 2005            // 同步时间
#define CARD_ID_IIS_DEFAULT 2006          // IIS默认
#define CARD_ID_RESTART_EXPLORER 2007     // 重启EXPLORER
#define CARD_ID_PARTITION_ASSIST 2008     // 分区助手
#define CARD_ID_NET4X 2009                // NET4.X
#define CARD_ID_IIS_FULL 2010             // IIS完整安装
#define CARD_ID_RESTART_CLIPBOARD 2011    // 重启剪贴板
#define CARD_ID_VIRTUALIZATION_CHECK 2012 // 虚拟机检测
#define CARD_ID_POWER_OPTIONS 2013        // 电源选项
#define CARD_ID_RESTART_COMPUTER 2014     // 重启计算机

#define CARD_ID_ProgramAndFeatures 2016 // 程序和功能

// 运维工具区域 (3001-3099)
#define CARD_ID_MULTI_INSTALL 3001          // 多界面安装
#define CARD_ID_MULTI_CONFIG 3002           // 多界面配置
#define CARD_ID_MULTI_UNINSTALL 3003        // 多界面卸载
#define CARD_ID_REMOTE_PORT 3004            // 修改远程端口
#define CARD_ID_PRIME95 3005                // Prime95
#define CARD_ID_LUDASHI 3006                // 鲁大师
#define CARD_ID_GEEKBENCH 3007              // Geekbench
#define CARD_ID_CINEBENCH 3008              // CineBench
#define CARD_ID_DISKMARK 3009               // DiskMark
#define CARD_ID_RESTART_REMOTE_SERVICE 3010 // 重启远程服务
#define CARD_ID_AIDA64 3011                 // Aida64
#define CARD_ID_SYSTEM_MONITOR 3012         // 系统监控

#define CARD_ID_CPUZ 3014        // CPU-Z


// 常用软件区域 (4001-4099)
#define CARD_ID_CYRJ_BAIDU_NETDISK 4001   // 百度网盘
#define CARD_ID_CYRJ_OLD_BAIDU 4002       // 老百度盘
#define CARD_ID_CYRJ_PINYIN_INPUT 4003    // 拼音输入法
#define CARD_ID_CYRJ_DBC2000 4004         // DBC_2000
#define CARD_ID_CYRJ_COMMON_BROWSERS 4005 // 常用浏览器
#define CARD_ID_CYRJ_360_CLOUD 4006       // 360云盘
#define CARD_ID_CYRJ_TENCENT_WEIYUN 4007  // 腾讯微云
#define CARD_ID_CYRJ_WUBI_INPUT 4008      // 五笔输入法
#define CARD_ID_CYRJ_MSSQL 4009           // MS_SQL
#define CARD_ID_CYRJ_CUSTOM_ENV 4010      // 定制环境
#define CARD_ID_CYRJ_ALI_DRIVE 4011       // 阿里云盘

#define CARD_ID_CYRJ_MS_RUNTIME 4013         // 微软运行库
#define CARD_ID_CYRJ_PROCESS_EXPLORER 4014   // ProcessExplorer
#define CARD_ID_CYRJ_SHOW_DESKTOP_ICONS 4015 // 显示桌面图标

// 产品福利区域 - 云安全 (5001-5099)
#define CARD_ID_CPFL_DDOS_DEFENSE 5001       // DDoS防御
#define CARD_ID_CPFL_SECURE_CDN 5002         // 安全加速SCDN
#define CARD_ID_CPFL_ANTI_D_SHIELD 5003      // 抗D盾
#define CARD_ID_CPFL_SSL_CERTIFICATE 5004    // SSL证书
#define CARD_ID_CPFL_APP_ACCELERATION 5005   // 应用加速
#define CARD_ID_CPFL_VULNERABILITY_SCAN 5006 // 漏洞扫描服务

// 产品福利区域 - 业务安全 (5101-5199)
#define CARD_ID_CPFL_GUARD 5101            // 德迅卫士
#define CARD_ID_CPFL_CLOUD_MONITORING 5102 // 云监测
#define CARD_ID_CPFL_HIVE_CONTAINER 5103   // 德迅蜂巢
#define CARD_ID_CPFL_HONEYPOT 5104         // 蜜罐
#define CARD_ID_CPFL_ZERO_TRUST 5105       // 德迅零域
#define CARD_ID_CPFL_CLOUD_ATLAS 5106      // 德迅云图

// 产品福利区域 - 安全运营 (5201-5299)
#define CARD_ID_CPFL_EXPERT_SERVICE 5201        // 专家服务
#define CARD_ID_CPFL_COMPLIANCE_CONSULTING 5202 // 等保合规咨询
#define CARD_ID_CPFL_SITUATION_AWARENESS 5203   // 态势感知
#define CARD_ID_CPFL_LOG_AUDIT 5204             // 日志审计
#define CARD_ID_CPFL_DATABASE_AUDIT 5205        // 数据库审计

// 产品福利区域 - 安全服务 (5301-5399)
#define CARD_ID_CPFL_PENETRATION_TEST 5301 // 渗透测试
#define CARD_ID_CPFL_CODE_AUDIT 5302       // 代码审计
#define CARD_ID_CPFL_RISK_ASSESSMENT 5303  // 风险评估
#define CARD_ID_CPFL_SECURITY_DRILL 5304   // 信息安全对抗演习
#define CARD_ID_CPFL_EMERGENCY_CENTER 5305 // 网络安全应急中心

// 未知卡片
#define CARD_ID_UNKNOWN 9999

#endif // CARDIDS_H
