<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6A929FDE-0EEF-3C97-A055-C1E7A1E7680B}</ProjectGuid>
    <RootNamespace>DxwsApp</RootNamespace>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.26100.0</WindowsTargetPlatformMinVersion>
    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <OutputDirectory>release\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>release\</IntermediateDirectory>
    <PrimaryOutput>DxwsApp</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v143</PlatformToolset>
    <OutputDirectory>debug\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>debug\</IntermediateDirectory>
    <PrimaryOutput>DxwsApp</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <QtInstall>5.15.2_msvc2019_64</QtInstall>
    <QtModules>core;network;gui;widgets</QtModules>
  </PropertyGroup>
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <QtInstall>5.15.2_msvc2019_64</QtInstall>
    <QtModules>core;network;gui;widgets</QtModules>
  </PropertyGroup>
  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">
    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />
  </Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">DxwsApp</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</IgnoreImportLibrary>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">DxwsApp</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</IgnoreImportLibrary>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;release;/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>release\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>release\</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;WINVER=0x0601;_WIN32_WINNT=0x0601;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;NDEBUG;QT_NO_DEBUG;ENABLE_DEBUG_WINDOW=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <UseFullPaths>true</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <AdditionalDependencies>advapi32.lib;shell32.lib;dbghelp.lib;psapi.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\openssl\lib;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <GenerateMapFile>true</GenerateMapFile>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkIncremental>false</LinkIncremental>
      <MapFileName>./DxwsApp.map</MapFileName>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)\DxwsApp.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <Version>1.0</Version>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;WINVER=0x0601;_WIN32_WINNT=0x0601;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;NDEBUG;QT_NO_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>./$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
    <QtRcc>
      <InitFuncName>res</InitFuncName>
      <Compression>default</Compression>
      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>
      <QtRccDir>$(Configuration)</QtRccDir>
      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>
    </QtRcc>
    <QtUic>
      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>
      <QtUicDir>$(ProjectDir)</QtUicDir>
      <QtUicFileName>ui_%(Filename).h</QtUicFileName>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;debug;/include;C:\Users\<USER>\source\repos\spdlog\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 /utf-8 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>debug\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>debug\</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;WINVER=0x0601;_WIN32_WINNT=0x0601;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;ENABLE_DEBUG_WINDOW=1;SPDLOG_ACTIVE_LEVEL=SPDLOG_LEVEL_TRACE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <UseFullPaths>true</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp23</LanguageStandard>
    </ClCompile>
    <Link>
      <AdditionalDependencies>advapi32.lib;shell32.lib;dbghelp.lib;psapi.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\openssl\lib;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <GenerateMapFile>true</GenerateMapFile>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkIncremental>false</LinkIncremental>
      <MapFileName>./DxwsApp.map</MapFileName>
      <OutputFile>$(OutDir)\DxwsApp.exe</OutputFile>
      <ProgramDatabaseFile>./debug/DxwsApp.pdb</ProgramDatabaseFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <Version>1.0</Version>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;WINVER=0x0601;_WIN32_WINNT=0x0601;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>./$(Configuration)/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>$(Configuration)</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
    <QtRcc>
      <InitFuncName>res</InitFuncName>
      <Compression>default</Compression>
      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>
      <QtRccDir>$(Configuration)</QtRccDir>
      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>
    </QtRcc>
    <QtUic>
      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>
      <QtUicDir>$(ProjectDir)</QtUicDir>
      <QtUicFileName>ui_%(Filename).h</QtUicFileName>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="appdatamanager.cpp" />
    <ClCompile Include="basemanager.cpp" />
    <ClCompile Include="cardwgt.cpp" />
    <ClCompile Include="confwgt.cpp" />
    <ClCompile Include="cpflcardwgt.cpp" />
    <ClCompile Include="cpflmanager.cpp" />
    <ClCompile Include="cyrjmanager.cpp" />
    <ClCompile Include="debugwindow.cpp" />
    <ClCompile Include="dxwswgt.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="mycommonwgt.cpp" />
    <ClCompile Include="opstoolwgt.cpp" />
    <ClCompile Include="popmenuwgt.cpp" />
    <ClCompile Include="wdgjmanager.cpp" />
    <ClCompile Include="ycycmanager.cpp" />
    <ClCompile Include="ywgjmanager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="appdatamanager.h" />
    <QtMoc Include="basemanager.h" />
    <QtMoc Include="cardwgt.h" />
    <QtMoc Include="confwgt.h" />
    <QtMoc Include="cpflcardwgt.h" />
    <QtMoc Include="cpflmanager.h" />
    <QtMoc Include="cyrjmanager.h" />
    <QtMoc Include="debugwindow.h" />
    <QtMoc Include="dxwswgt.h" />
    <QtMoc Include="mycommonwgt.h" />
    <QtMoc Include="opstoolwgt.h" />
    <QtMoc Include="popmenuwgt.h" />
    <QtMoc Include="wdgjmanager.h" />
    <QtMoc Include="ycycmanager.h" />
    <QtMoc Include="ywgjmanager.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus /EHsc /FC -Zi -MDd /Zi -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus /EHsc /FC -O2 -Zi -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;$(IntDir)\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="cardwgt.ui" />
    <QtUic Include="confwgt.ui" />
    <QtUic Include="cpflcardwgt.ui" />
    <QtUic Include="dxwswgt.ui" />
    <QtUic Include="mycommonwgt.ui" />
    <QtUic Include="opstoolwgt.ui" />
    <QtUic Include="popmenuwgt.ui" />
  </ItemGroup>
  <ItemGroup>
    <None Include="config\sub\software\360CloudDisk.bat" />
    <None Include="img\360_normal.png" />
    <None Include="img\360_simple.png" />
    <None Include="config\7z.dll" />
    <None Include="config\7z.exe" />
    <None Include="config\sub\ycyc\AIDA64.bat" />
    <None Include="config\sub\software\AliCloudDisk.bat" />
    <None Include="config\sub\software\BaiduNetdisk.bat" />
    <None Include="config\sub\ycyc\CPUZ.bat" />
    <None Include="config\sub\ycyc\Cinebench.bat" />
    <None Include="config\sub\software\CommonBrowsers.bat" />
    <None Include="config\sub\software\CustomEnvironment.bat" />
    <None Include="config\sub\ycyc\Diskmark.bat" />
    <None Include="config\sub\software\FastGuard.bat" />
    <None Include="config\sub\ycyc\Geekbench.bat" />
    <None Include="config\sub\ywgj\IISDefaultInstall.bat" />
    <None Include="config\sub\ywgj\IISFullInstall.bat" />
    <None Include="img\Icon_1.png" />
    <None Include="config\sub\ycyc\LuDashi.bat" />
    <None Include="config\sub\software\MicrosoftRuntime.bat" />
    <None Include="config\sub\software\OldBaiduDisk.bat" />
    <None Include="config\sub\ywgj\PartitionAssistant.bat" />
    <None Include="config\sub\software\PinyinInputIME.bat" />
    <None Include="config\sub\ycyc\Prime95.bat" />
    <None Include="config\sub\software\ProcessExplorer.bat" />
    <None Include="config\RDPWrap\RDPCheck.exe" />
    <None Include="config\RDPWrap\RDPConf.exe" />
    <None Include="config\RDPWrap\RDPWInst.exe" />
    <None Include="config\sub\ycyc\RestartRemoteService.bat" />
    <None Include="config\sub\software\ShowDesktopIcons.bat" />
    <None Include="config\sub\ycyc\SystemMonitor.exe" />
    <None Include="config\sub\software\TencentWeiyun.bat" />
    <None Include="config\sub\ywgj\VirtualizationDetection.bat" />
    <None Include="WindowsAdapter.ico" />
    <None Include="config\sub\ywgj\WindowsOptimizedImages.bat" />
    <None Include="config\sub\software\WubiInputIME.bat" />
    <None Include="config\axel.exe" />
    <None Include="img\ckjc.png" />
    <None Include="img\close.png" />
    <None Include="img\close2.png" />
    <None Include="img\close2_hover.png" />
    <None Include="img\close_hover.png" />
    <None Include="img\collect.png" />
    <None Include="img\collected.png" />
    <None Include="img\common_normal.png" />
    <None Include="img\common_normal_checked.png" />
    <None Include="img\common_normal_hover.png" />
    <None Include="img\common_simple.png" />
    <None Include="img\common_simple_checked.png" />
    <None Include="img\common_simple_hover.png" />
    <None Include="img\conf_btn.png" />
    <None Include="img\conf_title_bg.png" />
    <None Include="img\conf_title_logo.png" />
    <None Include="config\config.ini" />
    <None Include="img\cpfl_normal.png" />
    <None Include="img\cpfl_normal_checked.png" />
    <None Include="img\cpfl_normal_hover.png" />
    <None Include="img\cpfl_simple.png" />
    <None Include="img\cpfl_simple_checked.png" />
    <None Include="img\cpfl_simple_hover.png" />
    <None Include="config\cygwin1.dll" />
    <None Include="img\cyrj_normal.png" />
    <None Include="img\cyrj_normal_checked.png" />
    <None Include="img\cyrj_normal_hover.png" />
    <None Include="img\cyrj_simple.png" />
    <None Include="img\cyrj_simple_checked.png" />
    <None Include="img\cyrj_simple_hover.png" />
    <None Include="config\sub\software\dbc2000.bat" />
    <None Include="img\exit.png" />
    <None Include="img\Standard\img_software\ic_360clouddrive.png" />
    <None Include="img\Lite\img_software\ic_360clouddrive_2.png" />
    <None Include="img\Standard\img_ycyc\ic_aida64.png" />
    <None Include="img\Lite\img_ycyc\ic_aida64_2.png" />
    <None Include="img\Standard\img_software\ic_alibabaclouddrive.png" />
    <None Include="img\Lite\img_software\ic_alibabaclouddrive_2.png" />
    <None Include="img\Standard\img_cpfl\ic_anti_dshield.png" />
    <None Include="img\Lite\img_cpfl\ic_anti_dshield2.png" />
    <None Include="img\Standard\img_cpfl\ic_appaccel.png" />
    <None Include="img\Lite\img_cpfl\ic_appaccel2.png" />
    <None Include="img\Standard\img_cpfl\ic_audit.png" />
    <None Include="img\Lite\img_cpfl\ic_audit2.png" />
    <None Include="img\Standard\img_software\ic_baidupan.png" />
    <None Include="img\Lite\img_software\ic_baidupan_2.png" />
    <None Include="img\Standard\img_ycyc\ic_cinebench.png" />
    <None Include="img\Lite\img_ycyc\ic_cinebench_2.png" />
    <None Include="img\Standard\img_cpfl\ic_classifiedprotection.png" />
    <None Include="img\Lite\img_cpfl\ic_classifiedprotection2.png" />
    <None Include="img\Standard\img_cpfl\ic_cloudatlas.png" />
    <None Include="img\Lite\img_cpfl\ic_cloudatlas2.png" />
    <None Include="img\Standard\img_cpfl\ic_code.png" />
    <None Include="img\Lite\img_cpfl\ic_code2.png" />
    <None Include="img\Standard\img_software\ic_commonbrowsers.png" />
    <None Include="img\Lite\img_software\ic_commonbrowsers_2.png" />
    <None Include="img\Standard\img_ycyc\ic_cpu_z.png" />
    <None Include="img\Lite\img_ycyc\ic_cpu_z_2.png" />
    <None Include="img\Standard\img_software\ic_customenvironment.png" />
    <None Include="img\Lite\img_software\ic_customenvironment_2.png" />
    <None Include="img\Standard\img_cpfl\ic_database.png" />
    <None Include="img\Lite\img_cpfl\ic_database2.png" />
    <None Include="img\Standard\img_software\ic_dbc_2000.png" />
    <None Include="img\Lite\img_software\ic_dbc_2000_2.png" />
    <None Include="img\Standard\img_cpfl\ic_ddos.png" />
    <None Include="img\Lite\img_cpfl\ic_ddos2.png" />
    <None Include="img\Standard\img_ycyc\ic_diskmark.png" />
    <None Include="img\Lite\img_ycyc\ic_diskmark_2.png" />
    <None Include="img\Standard\img_software\ic_displaydesktop.png" />
    <None Include="img\Lite\img_software\ic_displaydesktop_2.png" />
    <None Include="img\Standard\img_cpfl\ic_drill.png" />
    <None Include="img\Lite\img_cpfl\ic_drill2.png" />
    <None Include="img\Standard\img_cpfl\ic_emergency.png" />
    <None Include="img\Lite\img_cpfl\ic_emergency2.png" />
    <None Include="img\Standard\img_cpfl\ic_expert.png" />
    <None Include="img\Lite\img_cpfl\ic_expert2.png" />
    <None Include="img\Standard\img_ycyc\ic_geekbench.png" />
    <None Include="img\Lite\img_ycyc\ic_geekbench_2.png" />
    <None Include="img\Standard\img_cpfl\ic_guard.png" />
    <None Include="img\Lite\img_cpfl\ic_guard2.png" />
    <None Include="img\Standard\img_cpfl\ic_hive.png" />
    <None Include="img\Lite\img_cpfl\ic_hive2.png" />
    <None Include="img\Standard\img_cpfl\ic_honeypot.png" />
    <None Include="img\Lite\img_cpfl\ic_honeypot2.png" />
    <None Include="img\Standard\img_ywgj\ic_iiscomplete.png" />
    <None Include="img\Lite\img_ywgj\ic_iiscomplete_2.png" />
    <None Include="img\Standard\img_ywgj\ic_iisdefault.png" />
    <None Include="img\Lite\img_ywgj\ic_iisdefault_2.png" />
    <None Include="img\Standard\img_ywgj\ic_kms.png" />
    <None Include="img\Lite\img_ywgj\ic_kms_2.png" />
    <None Include="img\Standard\img_ycyc\ic_masterlu.png" />
    <None Include="img\Lite\img_ycyc\ic_masterlu_2.png" />
    <None Include="img\Standard\img_software\ic_microsoftruntimelibrary.png" />
    <None Include="img\Lite\img_software\ic_microsoftruntimelibrary_2.png" />
    <None Include="img\Standard\img_ycyc\ic_modifyremoteport.png" />
    <None Include="img\Lite\img_ycyc\ic_modifyremoteport_2.png" />
    <None Include="img\Standard\img_cpfl\ic_monitoring.png" />
    <None Include="img\Lite\img_cpfl\ic_monitoring2.png" />
    <None Include="img\Standard\img_software\ic_ms_sql.png" />
    <None Include="img\Lite\img_software\ic_ms_sql_2.png" />
    <None Include="img\Standard\img_ycyc\ic_multi interface settings.png" />
    <None Include="img\Lite\img_ycyc\ic_multi interface settings_2.png" />
    <None Include="img\Standard\img_ycyc\ic_multiinterface uninstallation.png" />
    <None Include="img\Lite\img_ycyc\ic_multiinterface uninstallation_2.png" />
    <None Include="img\Standard\img_ycyc\ic_multiinterfaceinstallation.png" />
    <None Include="img\Lite\img_ycyc\ic_multiinterfaceinstallation_2.png" />
    <None Include="img\Standard\img_ywgj\ic_net3.5.png" />
    <None Include="img\Lite\img_ywgj\ic_net3.5_2.png" />
    <None Include="img\Standard\img_ywgj\ic_net4.x.png" />
    <None Include="img\Lite\img_ywgj\ic_net4.x_2.png" />
    <None Include="img\Standard\img_software\ic_oidbaidudrive.png" />
    <None Include="img\Lite\img_software\ic_oidbaidudrive_2.png" />
    <None Include="img\Standard\img_ycyc\ic_openthetoolstation.png" />
    <None Include="img\Lite\img_ycyc\ic_openthetoolstation_2.png" />
    <None Include="img\Standard\img_ywgj\ic_partitionassistant.png" />
    <None Include="img\Lite\img_ywgj\ic_partitionassistant_2.png" />
    <None Include="img\Standard\img_software\ic_pinyin.png" />
    <None Include="img\Lite\img_software\ic_pinyin_2.png" />
    <None Include="img\Standard\img_ywgj\ic_poweroptions.png" />
    <None Include="img\Lite\img_ywgj\ic_poweroptions_2.png" />
    <None Include="img\Standard\img_ycyc\ic_prime95.png" />
    <None Include="img\Lite\img_ycyc\ic_prime95_2.png" />
    <None Include="img\Standard\img_software\ic_processexplorer.png" />
    <None Include="img\Lite\img_software\ic_processexplorer_2.png" />
    <None Include="img\Standard\img_ywgj\ic_programsandfunctions.png" />
    <None Include="img\Lite\img_ywgj\ic_programsandfunctions_2.png" />
    <None Include="img\Standard\img_ywgj\ic_quick.png" />
    <None Include="img\Lite\img_ywgj\ic_quick_2.png" />
    <None Include="img\Standard\img_ycyc\ic_restartremoteservice.png" />
    <None Include="img\Lite\img_ycyc\ic_restartremoteservice_2.png" />
    <None Include="img\Standard\img_ywgj\ic_restarttheadhesiveboard.png" />
    <None Include="img\Lite\img_ywgj\ic_restarttheadhesiveboard_2.png" />
    <None Include="img\Standard\img_ywgj\ic_restartthecomputer.png" />
    <None Include="img\Lite\img_ywgj\ic_restartthecomputer_2.png" />
    <None Include="img\Standard\img_ywgj\ic_restarttheexplorer.png" />
    <None Include="img\Lite\img_ywgj\ic_restarttheexplorer_2.png" />
    <None Include="img\Standard\img_cpfl\ic_risk.png" />
    <None Include="img\Lite\img_cpfl\ic_risk2.png" />
    <None Include="img\Standard\img_cpfl\ic_scdn.png" />
    <None Include="img\Lite\img_cpfl\ic_scdn2.png" />
    <None Include="img\Standard\img_cpfl\ic_situation.png" />
    <None Include="img\Lite\img_cpfl\ic_situation2.png" />
    <None Include="img\Standard\img_cpfl\ic_ssl.png" />
    <None Include="img\Lite\img_cpfl\ic_ssl2.png" />
    <None Include="img\Standard\img_ycyc\ic_systemmonitor.png" />
    <None Include="img\Lite\img_ycyc\ic_systemmonitor_2.png" />
    <None Include="img\Standard\img_software\ic_tencentweiyun.png" />
    <None Include="img\Lite\img_software\ic_tencentweiyun_2.png" />
    <None Include="img\Standard\img_cpfl\ic_test.png" />
    <None Include="img\Lite\img_cpfl\ic_test2.png" />
    <None Include="img\Standard\img_ywgj\ic_timesynchronization.png" />
    <None Include="img\Lite\img_ywgj\ic_timesynchronization_2.png" />
    <None Include="img\Standard\img_ywgj\ic_virtualizationdetection.png" />
    <None Include="img\Lite\img_ywgj\ic_virtualizationdetection_2.png" />
    <None Include="img\Standard\img_cpfl\ic_vulnerability.png" />
    <None Include="img\Lite\img_cpfl\ic_vulnerability2.png" />
    <None Include="img\Standard\img_ywgj\ic_winoptimization.png" />
    <None Include="img\Lite\img_ywgj\ic_winoptimization_2.png" />
    <None Include="img\Standard\img_software\ic_wubi.png" />
    <None Include="img\Lite\img_software\ic_wubi_2.png" />
    <None Include="config\RDPWrap\install.bat" />
    <None Include="config\sub\ywgj\kms.bat" />
    <None Include="img\lbdp_normal.png" />
    <None Include="img\lbdp_simple.png" />
    <None Include="img\logo_left.png" />
    <None Include="img\logo_tray.png" />
    <None Include="img\max.png" />
    <None Include="img\min.png" />
    <None Include="config\sub\software\mssql.bat" />
    <None Include="img\navi_bg.png" />
    <None Include="config\sub\ywgj\net35.bat" />
    <None Include="config\sub\ywgj\net4x.bat" />
    <None Include="qss\normal.qss" />
    <None Include="img\pysrf_normal.png" />
    <None Include="img\pysrf_simple.png" />
    <None Include="img\r_bg_normal.png" />
    <None Include="img\r_bg_simple.png" />
    <QtRcc Include="res.qrc" />
    <None Include="img\restart.png" />
    <None Include="img\search.png" />
    <None Include="img\show_win.png" />
    <None Include="qss\simple.qss" />
    <None Include="img\theme_normal.png" />
    <None Include="img\theme_normal_checked.png" />
    <None Include="img\theme_simple.png" />
    <None Include="img\theme_simple_checked.png" />
    <None Include="img\txwy_normal.png" />
    <None Include="img\txwy_simple.png" />
    <None Include="config\RDPWrap\uninstall.bat" />
    <None Include="config\RDPWrap\update.bat" />
    <None Include="img\ycyc_normal.png" />
    <None Include="img\ycyc_normal_checked.png" />
    <None Include="img\ycyc_normal_hover.png" />
    <None Include="img\ycyc_simple.png" />
    <None Include="img\ycyc_simple_checked.png" />
    <None Include="img\ycyc_simple_hover.png" />
    <None Include="img\ywgj_normal.png" />
    <None Include="img\ywgj_normal_checked.png" />
    <None Include="img\ywgj_normal_hover.png" />
    <None Include="img\ywgj_simple.png" />
    <None Include="img\ywgj_simple_checked.png" />
    <None Include="img\ywgj_simple_hover.png" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\DxwsApp_resource.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />
  <ImportGroup Label="ExtensionTargets" />
</Project>