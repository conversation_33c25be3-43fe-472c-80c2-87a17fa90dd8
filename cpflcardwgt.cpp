#include "cpflcardwgt.h"
#include "ui_cpflcardwgt.h"
#include <QMouseEvent>

CpflCardWgt::CpflCardWgt(QWidget *parent) : QWidget(parent), ui(new Ui::CpflCardWgt)
{
    ui->setupUi(this);
}

CpflCardWgt::~CpflCardWgt()
{
    delete ui;
}

void CpflCardWgt::setNormal_icon_path(const QString &normal_icon_path)
{
    normal_icon_path_ = normal_icon_path;
}

void CpflCardWgt::setSimple_icon_path(const QString &simple_icon_path)
{
    simple_icon_path_ = simple_icon_path;
}

void CpflCardWgt::SetTheme(int type)
{
    type_ = type;
    switch (type)
    {
    case 0: {
        SetIcon(normal_icon_path_);
    }
    break;
    case 1: {
        SetIcon(simple_icon_path_);
    }
    break;
    }
}

void CpflCardWgt::SetName(QString name)
{
    ui->label_name->setText(name);
}

void CpflCardWgt::SetContent(QString content)
{
    ui->label_content->setText(content);
}

void CpflCardWgt::SetCardID(int cardID)
{
    card_id_ = cardID;
}

int CpflCardWgt::GetCardID() const
{
    return card_id_;
}

void CpflCardWgt::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit clicked();
    }
    QWidget::mousePressEvent(event);
}

void CpflCardWgt::SetIcon(QString path)
{
    QString style_sheet("border-image:url(\"%1\")");
    ui->label_icon->setStyleSheet(style_sheet.arg(path));
}
