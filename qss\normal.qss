/* 主窗口边框样式 */
QWidget#DxwsWgt
{
    border: 1px solid #E1E5E9;
    border-radius: 10px;
    background-color: #FFFFFF;
}

QWidget#wgt_navi
{
	border-image: url(:/img/navi_bg.png);
}

QWidget#wgt_r_bg
{
	border-image: url(:/img/r_bg_normal.png);
}

QPushButton#btn_wdgj
{
	border-image: url(:/img/common_normal.png);
}
QPushButton#btn_wdgj:hover
{
	
	border-image: url(:/img/common_normal_hover.png);
}
QPushButton#btn_wdgj:checked 
{
	
	border-image: url(:/img/common_normal_checked.png);
}

QPushButton#btn_ywgj
{
	border-image: url(:/img/ywgj_normal.png);
}
QPushButton#btn_ywgj:hover
{
	
	border-image: url(:/img/ywgj_normal_hover.png);
}
QPushButton#btn_ywgj:checked 
{
	
	border-image: url(:/img/ywgj_normal_checked.png);
}

QPushButton#btn_ycyc
{
	border-image: url(:/img/ycyc_normal.png);
}
QPushButton#btn_ycyc:hover
{
	
	border-image: url(:/img/ycyc_normal_hover.png);
}
QPushButton#btn_ycyc:checked 
{
	
	border-image: url(:/img/ycyc_normal_checked.png);
}

QPushButton#btn_cyrj
{
	border-image: url(:/img/cyrj_normal.png);
}
QPushButton#btn_cyrj:hover
{
	
	border-image: url(:/img/cyrj_normal_hover.png);
}
QPushButton#btn_cyrj:checked 
{
	
	border-image: url(:/img/cyrj_normal_checked.png);
}

QPushButton#btn_cpfl
{
	border-image: url(:/img/cpfl_normal.png);
}
QPushButton#btn_cpfl:hover
{
	
	border-image: url(:/img/cpfl_normal_hover.png);
}
QPushButton#btn_cpfl:checked 
{
	
	border-image: url(:/img/cpfl_normal_checked.png);
}