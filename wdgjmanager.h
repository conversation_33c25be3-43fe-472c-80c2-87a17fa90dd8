#ifndef WDGJMANAGER_H
#define WDGJMANAGER_H

#include "basemanager.h"
#include "cardwgt.h"
#include <QList>
#include <QObject>
#include <QWidget>

class WdgjManager : public BaseManager
{
    Q_OBJECT

  public:
    explicit WdgjManager(QWidget *parent = nullptr);
    ~WdgjManager();

    // 初始化我的工具相关功能
    void InitWdgj();

    // 添加工具卡片
    void AddWdgjCommon(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId);
    void AddWdgjCollected(const QString &normal_icon, const QString &simple_icon, const QString &name, int cardId);

    // 获取卡片列表
    QList<CardWgt *> &GetCommonCards()
    {
        return wdgj_common_cards_;
    }
    QList<CardWgt *> &GetCollectedCards()
    {
        return wdgj_collected_cards_;
    }

    // 处理卡片收藏事件
    void HandleCardCollect(int cardId, bool isCollected);

    void AddCardToCollected(CardWgt *card);
    void RemoveCardFromCollected(int cardId);

  private:
    QList<CardWgt *> wdgj_common_cards_;
    QList<CardWgt *> wdgj_collected_cards_;
};

#endif // WDGJMANAGER_H
