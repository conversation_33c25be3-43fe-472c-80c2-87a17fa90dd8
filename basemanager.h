#ifndef BASEMANAGER_H
#define BASEMANAGER_H

#include <QObject>
#include <QString>
#include <QWidget>

class BaseManager : public QObject
{
    Q_OBJECT
  signals:
    void cardClicked(int cardId);
    void collectClicked(int cardId, bool isCollected);

  public:
    explicit BaseManager(QWidget *parent = nullptr);
    virtual ~BaseManager() = default;

  protected:
    // 公共的配置文件执行函数
    void ExecuteConfigFile(const QString &relativePath, const QString &description);

  protected:
    QWidget *parent_widget_;
};

#endif // BASEMANAGER_H
