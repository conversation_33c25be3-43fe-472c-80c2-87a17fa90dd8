#ifndef POPMENUWGT_H
#define POPMENUWGT_H

#include <QWidget>

#include "cardwgt.h"

namespace Ui
{
class PopMenuWgt;
}

class PopMenuWgt : public QWidget
{
    Q_OBJECT

  public:
    explicit PopMenuWgt(QWidget *parent = nullptr);
    ~PopMenuWgt();

    void SetCards(QList<CardWgt *> &cards);

  signals:
    void cardClicked(int cardId);

  private slots:
    void OnCardButtonClicked();

  private:
    void Clear();

  private:
    Ui::PopMenuWgt *ui;
    QList<CardWgt *> current_cards_;
};

#endif // POPMENUWGT_H
