<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DxwsWgt</class>
 <widget class="QWidget" name="DxwsWgt">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>708</width>
    <height>486</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>德迅工具箱</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="wgt_navi" native="true">
     <property name="minimumSize">
      <size>
       <width>136</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>30</number>
      </property>
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>16</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>16</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <property name="leftMargin">
         <number>10</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <item>
         <widget class="QLabel" name="label">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>20</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">border-image: url(:/img/logo_left.png);</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QPushButton" name="btn_wdgj">
          <property name="minimumSize">
           <size>
            <width>116</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
          <attribute name="buttonGroup">
           <string notr="true">buttonGroup_left</string>
          </attribute>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_ywgj">
          <property name="minimumSize">
           <size>
            <width>116</width>
            <height>40</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
          <attribute name="buttonGroup">
           <string notr="true">buttonGroup_left</string>
          </attribute>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_ycyc">
          <property name="minimumSize">
           <size>
            <width>116</width>
            <height>40</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
          <attribute name="buttonGroup">
           <string notr="true">buttonGroup_left</string>
          </attribute>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_cyrj">
          <property name="minimumSize">
           <size>
            <width>116</width>
            <height>40</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <attribute name="buttonGroup">
           <string notr="true">buttonGroup_left</string>
          </attribute>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_cpfl">
          <property name="minimumSize">
           <size>
            <width>116</width>
            <height>40</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
          <attribute name="buttonGroup">
           <string notr="true">buttonGroup_left</string>
          </attribute>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>124</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_3">
        <property name="font">
         <font>
          <family>Microsoft YaHei</family>
          <pointsize>-1</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">font-family: Microsoft YaHei;
font-size: 14px;
color: #0665FF;</string>
        </property>
        <property name="text">
         <string>了解德迅的产品</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,1">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="wgt_title" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>46</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background: #FFFFFF;
border-top-left-radius:0px;
border-top-right-radius:6px;
border-bottom-left-radius:0px;
border-bottom-right-radius:0px;</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="leftMargin">
         <number>24</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QWidget" name="wgt_search" native="true">
          <property name="minimumSize">
           <size>
            <width>240</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>240</width>
            <height>26</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">border-radius: 6px;
border: 1px solid #E0E0E2;</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,1,0">
           <property name="leftMargin">
            <number>11</number>
           </property>
           <property name="topMargin">
            <number>1</number>
           </property>
           <property name="rightMargin">
            <number>12</number>
           </property>
           <property name="bottomMargin">
            <number>1</number>
           </property>
           <item>
            <widget class="QPushButton" name="btn_search">
             <property name="minimumSize">
              <size>
               <width>11</width>
               <height>11</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>11</width>
               <height>11</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border:none;
border-image: url(:/img/search.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_search">
             <property name="styleSheet">
              <string notr="true">border:none;
background:transparent;
font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 10px;
color: #111111;</string>
             </property>
             <property name="placeholderText">
              <string>搜索...</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_clear">
             <property name="minimumSize">
              <size>
               <width>7</width>
               <height>7</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>7</width>
               <height>7</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border:none;
border-image: url(:/img/close.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>125</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="btn_conf">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>46</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton
{
background: transparent;
}
QPushButton:hover
{
background: #ECEFF4;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset resource="res.qrc">
              <normaloff>:/img/conf_btn.png</normaloff>:/img/conf_btn.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_min">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>46</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton
{
background: transparent;
}
QPushButton:hover
{
background: #ECEFF4;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset resource="res.qrc">
              <normaloff>:/img/min.png</normaloff>:/img/min.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_max">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>46</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton
{
background: transparent;
}
QPushButton:hover
{
background: #ECEFF4;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset resource="res.qrc">
              <normaloff>:/img/max.png</normaloff>:/img/max.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_close">
            <property name="minimumSize">
             <size>
              <width>46</width>
              <height>46</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton
{
background: transparent;
	border-image: url(:/img/close2.png);
	border-top-left-radius:0px;
    border-top-right-radius:6px;
    border-bottom-left-radius:0px;
    border-bottom-right-radius:0px;
}
QPushButton:hover
{
	border-image: url(:/img/close2_hover.png);
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="wgt_r_bg" native="true">
       <layout class="QVBoxLayout" name="verticalLayout" stretch="1,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QStackedWidget" name="stackedWidget">
          <property name="minimumSize">
           <size>
            <width>570</width>
            <height>27</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background:transparent;

</string>
          </property>
          <widget class="MyCommonWgt" name="page_wdgj">
           <layout class="QVBoxLayout" name="verticalLayout_7">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>4</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea">
              <property name="styleSheet">
               <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
              </property>
              <property name="verticalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOn</enum>
              </property>
              <property name="horizontalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOff</enum>
              </property>
              <property name="widgetResizable">
               <bool>false</bool>
              </property>
              <widget class="QWidget" name="sawc_wdgj">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>562</width>
                 <height>409</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_10" stretch="0,0,1">
                <property name="leftMargin">
                 <number>24</number>
                </property>
                <property name="topMargin">
                 <number>24</number>
                </property>
                <property name="rightMargin">
                 <number>24</number>
                </property>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_5" stretch="0,1">
                  <property name="spacing">
                   <number>16</number>
                  </property>
                  <item>
                   <widget class="QLabel" name="label_4">
                    <property name="styleSheet">
                     <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #111111;</string>
                    </property>
                    <property name="text">
                     <string>我的常用</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QWidget" name="wgt_common" native="true"/>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QVBoxLayout" name="verticalLayout_6" stretch="0,1">
                  <property name="spacing">
                   <number>16</number>
                  </property>
                  <item>
                   <widget class="QLabel" name="label_5">
                    <property name="styleSheet">
                     <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #111111;</string>
                    </property>
                    <property name="text">
                     <string>我的收藏</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QWidget" name="wgt_collected" native="true"/>
                  </item>
                 </layout>
                </item>
                <item>
                 <spacer name="verticalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>111</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_cyrj">
           <layout class="QVBoxLayout" name="verticalLayout_11">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>4</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea_2">
              <property name="styleSheet">
               <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
              </property>
              <property name="verticalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOn</enum>
              </property>
              <property name="horizontalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOff</enum>
              </property>
              <property name="widgetResizable">
               <bool>false</bool>
              </property>
              <widget class="QWidget" name="sawc_cyrj">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>562</width>
                 <height>409</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9" stretch="0,1">
                <property name="leftMargin">
                 <number>24</number>
                </property>
                <property name="topMargin">
                 <number>24</number>
                </property>
                <property name="rightMargin">
                 <number>24</number>
                </property>
                <item>
                 <widget class="QWidget" name="wgt_cyrj" native="true"/>
                </item>
                <item>
                 <spacer name="verticalSpacer_4">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>331</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="OpsToolWgt" name="page_ywgj">
           <layout class="QVBoxLayout" name="verticalLayout_12">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>4</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea_3">
              <property name="styleSheet">
               <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
              </property>
              <property name="verticalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOn</enum>
              </property>
              <property name="horizontalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOff</enum>
              </property>
              <property name="widgetResizable">
               <bool>false</bool>
              </property>
              <widget class="QWidget" name="sawc_ywgj">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>562</width>
                 <height>409</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_8" stretch="0,1">
                <property name="leftMargin">
                 <number>24</number>
                </property>
                <property name="topMargin">
                 <number>24</number>
                </property>
                <property name="rightMargin">
                 <number>24</number>
                </property>
                <item>
                 <widget class="QWidget" name="wgt_ywgj" native="true"/>
                </item>
                <item>
                 <spacer name="verticalSpacer_3">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>321</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_ycyc">
           <layout class="QVBoxLayout" name="verticalLayout_ycyc">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>4</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea_ycyc">
              <property name="styleSheet">
               <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
              </property>
              <property name="verticalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOn</enum>
              </property>
              <property name="horizontalScrollBarPolicy">
               <enum>Qt::ScrollBarAlwaysOff</enum>
              </property>
              <property name="widgetResizable">
               <bool>false</bool>
              </property>
              <widget class="QWidget" name="sawc_ycyc">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>562</width>
                 <height>409</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_ycyc_inner" stretch="0,1">
                <property name="leftMargin">
                 <number>24</number>
                </property>
                <property name="topMargin">
                 <number>24</number>
                </property>
                <property name="rightMargin">
                 <number>24</number>
                </property>
                <item>
                 <widget class="QWidget" name="wgt_ycyc" native="true"/>
                </item>
                <item>
                 <spacer name="verticalSpacer_ycyc">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>331</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
<widget class="QWidget" name="page_cpfl">
           <layout class="QVBoxLayout" name="verticalLayout_14" stretch="0,0,0">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>25</number>
            </property>
            <property name="topMargin">
             <number>19</number>
            </property>
            <property name="rightMargin">
             <number>25</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QPushButton" name="btn_yaq">
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
border:none;

}
QPushButton:hover, QPushButton:checked
{
font-family: Microsoft YaHei;
font-weight: 500;
font-size: 12px;
color: #0665FF;
border-bottom:1px solid #0665FF;
}</string>
                </property>
                <property name="text">
                 <string>云安全</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup_cpfl</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btn_ywaq">
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
border:none;

}
QPushButton:hover, QPushButton:checked
{
font-family: Microsoft YaHei;
font-weight: 500;
font-size: 12px;
color: #0665FF;
border-bottom:1px solid #0665FF;
}</string>
                </property>
                <property name="text">
                 <string>业务安全</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup_cpfl</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btn_aqyy">
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
border:none;

}
QPushButton:hover, QPushButton:checked
{
font-family: Microsoft YaHei;
font-weight: 500;
font-size: 12px;
color: #0665FF;
border-bottom:1px solid #0665FF;
}</string>
                </property>
                <property name="text">
                 <string>安全运营</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup_cpfl</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btn_aqfw">
                <property name="minimumSize">
                 <size>
                  <width>96</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
border:none;

}
QPushButton:hover, QPushButton:checked
{
font-family: Microsoft YaHei;
font-weight: 500;
font-size: 12px;
color: #0665FF;
border-bottom:1px solid #0665FF;
}</string>
                </property>
                <property name="text">
                 <string>安全服务</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup_cpfl</string>
                </attribute>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QLabel" name="label_6">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>1</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>1</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">border-radius: 0px 0px 0px 0px;
border: 1px solid #FFFFFF;</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QStackedWidget" name="stackedWidget_2">
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="page_yaq">
               <layout class="QVBoxLayout" name="verticalLayout_15">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="scrollArea_yaq">
                  <property name="styleSheet">
                   <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
                  </property>
                  <property name="verticalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOn</enum>
                  </property>
                  <property name="horizontalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOff</enum>
                  </property>
                  <property name="widgetResizable">
                   <bool>false</bool>
                  </property>
                  <widget class="QWidget" name="sawc_yaq">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>516</width>
                     <height>357</height>
                    </rect>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_13" stretch="0,1">
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>16</number>
                    </property>
                    <property name="rightMargin">
                     <number>12</number>
                    </property>
                    <item>
                     <widget class="QWidget" name="wgt_yaq" native="true"/>
                    </item>
                    <item>
                     <spacer name="verticalSpacer_5">
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>20</width>
                        <height>331</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_aqfw">
               <layout class="QVBoxLayout" name="verticalLayout_19">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="scrollArea_aqfw">
                  <property name="styleSheet">
                   <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
                  </property>
                  <property name="verticalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOn</enum>
                  </property>
                  <property name="horizontalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOff</enum>
                  </property>
                  <property name="widgetResizable">
                   <bool>false</bool>
                  </property>
                  <widget class="QWidget" name="sawc_aqfw">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>516</width>
                     <height>357</height>
                    </rect>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_18" stretch="0,1">
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>16</number>
                    </property>
                    <property name="rightMargin">
                     <number>12</number>
                    </property>
                    <item>
                     <widget class="QWidget" name="wgt_aqfw" native="true"/>
                    </item>
                    <item>
                     <spacer name="verticalSpacer_7">
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>20</width>
                        <height>331</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_ywaq">
               <layout class="QVBoxLayout" name="verticalLayout_17">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="scrollArea_ywaq">
                  <property name="styleSheet">
                   <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
                  </property>
                  <property name="verticalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOn</enum>
                  </property>
                  <property name="horizontalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOff</enum>
                  </property>
                  <property name="widgetResizable">
                   <bool>false</bool>
                  </property>
                  <widget class="QWidget" name="sawc_ywaq">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>516</width>
                     <height>357</height>
                    </rect>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_16" stretch="0,1">
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>16</number>
                    </property>
                    <property name="rightMargin">
                     <number>12</number>
                    </property>
                    <item>
                     <widget class="QWidget" name="wgt_ywaq" native="true"/>
                    </item>
                    <item>
                     <spacer name="verticalSpacer_6">
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>20</width>
                        <height>331</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_aqyy">
               <layout class="QVBoxLayout" name="verticalLayout_21">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="scrollArea_aqyy">
                  <property name="styleSheet">
                   <string notr="true">QScrollArea
{
border:none;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }
</string>
                  </property>
                  <property name="verticalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOn</enum>
                  </property>
                  <property name="horizontalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOff</enum>
                  </property>
                  <property name="widgetResizable">
                   <bool>false</bool>
                  </property>
                  <widget class="QWidget" name="sawc_aqyy">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>516</width>
                     <height>357</height>
                    </rect>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_20" stretch="0,1">
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>16</number>
                    </property>
                    <property name="rightMargin">
                     <number>12</number>
                    </property>
                    <item>
                     <widget class="QWidget" name="wgt_aqyy" native="true"/>
                    </item>
                    <item>
                     <spacer name="verticalSpacer_8">
                      <property name="orientation">
                       <enum>Qt::Vertical</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>20</width>
                        <height>331</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="wgt_status" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>27</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>27</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background: #FFFFFF;

	border-top-left-radius:0px;
    border-top-right-radius:0px;
    border-bottom-left-radius:0px;
    border-bottom-right-radius:0px;</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,1">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>24</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="styleSheet">
              <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 10px;
color: #7A869A;</string>
             </property>
             <property name="text">
              <string></string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>263</width>
               <height>6</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyCommonWgt</class>
   <extends>QWidget</extends>
   <header>mycommonwgt.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>OpsToolWgt</class>
   <extends>QWidget</extends>
   <header>opstoolwgt.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="res.qrc"/>
 </resources>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup_left"/>
  <buttongroup name="buttonGroup_cpfl"/>
 </buttongroups>
</ui>
