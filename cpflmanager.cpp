#include "cpflmanager.h"
#include <QLayoutItem>
#include <QMessageBox>
#include <QtMath>

CpflManager::CpflManager(QObject *parent) : QObject(parent)
{
    // 初始化各个类别的卡片
    InitYaq();
    InitYwaq();
    InitAqyy();
    InitAqfw();
}

void CpflManager::Init(QWidget *yaq_container, QWidget *yaq_scroll, QWidget *ywaq_container, QWidget *ywaq_scroll,
                       QWidget *aqyy_container, QWidget *aqyy_scroll, QWidget *aqfw_container, QWidget *aqfw_scroll)
{
    UpdateYaq(yaq_container, yaq_scroll);
    UpdateYwaq(ywaq_container, ywaq_scroll);
    UpdateAqyy(aqyy_container, aqyy_scroll);
    UpdateAqfw(aqfw_container, aqfw_scroll);
}

CpflManager::~CpflManager()
{
    // 清理卡片
    qDeleteAll(yaq_cpfl_cards_);
    qDeleteAll(ywaq_cpfl_cards_);
    qDeleteAll(aqyy_cpfl_cards_);
    qDeleteAll(aqfw_cpfl_cards_);
}

void CpflManager::AddYaq(const QString &normal_icon, const QString &simple_icon, const QString &name,
                         const QString &content, int cardID)
{
    CpflCardWgt *card = new CpflCardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetContent(content);
    card->SetCardID(cardID);
    card->SetTheme(current_theme_);

    // 连接点击信号
    connect(card, &CpflCardWgt::clicked, this, &CpflManager::OnCardDetailClicked);

    yaq_cpfl_cards_.push_back(card);
}

void CpflManager::AddYwaq(const QString &normal_icon, const QString &simple_icon, const QString &name,
                          const QString &content, int cardID)
{
    CpflCardWgt *card = new CpflCardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetContent(content);
    card->SetCardID(cardID);
    card->SetTheme(current_theme_);

    // 连接点击信号
    connect(card, &CpflCardWgt::clicked, this, &CpflManager::OnCardDetailClicked);

    ywaq_cpfl_cards_.push_back(card);
}

void CpflManager::AddAqyy(const QString &normal_icon, const QString &simple_icon, const QString &name,
                          const QString &content, int cardID)
{
    CpflCardWgt *card = new CpflCardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetContent(content);
    card->SetCardID(cardID);
    card->SetTheme(current_theme_);

    // 连接点击信号
    connect(card, &CpflCardWgt::clicked, this, &CpflManager::OnCardDetailClicked);

    aqyy_cpfl_cards_.push_back(card);
}

void CpflManager::AddAqfw(const QString &normal_icon, const QString &simple_icon, const QString &name,
                          const QString &content, int cardID)
{
    CpflCardWgt *card = new CpflCardWgt;
    card->setNormal_icon_path(normal_icon);
    card->setSimple_icon_path(simple_icon);
    card->SetName(name);
    card->SetContent(content);
    card->SetCardID(cardID);
    card->SetTheme(current_theme_);

    // 连接点击信号
    connect(card, &CpflCardWgt::clicked, this, &CpflManager::OnCardDetailClicked);

    aqfw_cpfl_cards_.push_back(card);
}

void CpflManager::InitYaq()
{
    AddYaq(":/img/Standard/img_cpfl/ic_ddos.png", ":/img/Lite/img_cpfl/ic_ddos2.png", "DDoS防御",
           "专注于特大流量DDoS攻击防御，防护能力超过6T，保障您的业务不中断", CARD_ID_CPFL_DDOS_DEFENSE);
    AddYaq(":/img/Standard/img_cpfl/ic_scdn.png", ":/img/Lite/img_cpfl/ic_scdn2.png", "安全加速SCDN",
           "集分布式DDoS防护、CC防护、WAF防护为一体的安全加速解决方案", CARD_ID_CPFL_SECURE_CDN);
    AddYaq(":/img/Standard/img_cpfl/ic_anti_dshield.png", ":/img/Lite/img_cpfl/ic_anti_dshield2.png",
           "抗D盾(基于TCP协议的业务云防护)",
           "能针对大型DDoS攻击（T级别）有效防御外，还能彻底解决游戏特有的TCP协议的CC攻击问题，防护成本更低，效果更好！",
           CARD_ID_CPFL_ANTI_D_SHIELD);
    AddYaq(":/img/Standard/img_cpfl/ic_ssl.png", ":/img/Lite/img_cpfl/ic_ssl2.png", "SSL证书(数据加密安全防劫持)",
           "将网址中的“HTTP”升级为“HTTPS”协议，并使浏览器对网站加注“安全”标识", CARD_ID_CPFL_SSL_CERTIFICATE);
    AddYaq(":/img/Standard/img_cpfl/ic_appaccel.png", ":/img/Lite/img_cpfl/ic_appaccel2.png", "应用加速（客户端安全）",
           "能针对大型DDoS攻击（T级别）有效防御外，还能彻底解决游戏特有的TCP协议的CC攻击问题，防护成本更低，效果更好！",
           CARD_ID_CPFL_APP_ACCELERATION);
    AddYaq(":/img/Standard/img_cpfl/ic_vulnerability.png", ":/img/Lite/img_cpfl/ic_vulnerability2.png",
           "漏洞扫描服务 VSS", "将网址中的“HTTP”升级为“HTTPS”协议，并使浏览器对网站加注“安全”标识",
           CARD_ID_CPFL_VULNERABILITY_SCAN);
}

void CpflManager::InitYwaq()
{
    AddYwaq(":/img/Standard/img_cpfl/ic_guard.png", ":/img/Lite/img_cpfl/ic_guard2.png", "德迅卫士(入侵防护)",
            "德迅云安全全力打造，专注于事前拦截，事后溯源", CARD_ID_CPFL_GUARD);
    AddYwaq(":/img/Standard/img_cpfl/ic_monitoring.png", ":/img/Lite/img_cpfl/ic_monitoring2.png", "云监测",
            "基于AI+大数据的下一代网络安全监测治理平台，提供脆弱性、可用性、资产发现、安全事件等多项监测能力",
            CARD_ID_CPFL_CLOUD_MONITORING);
    AddYwaq(
        ":/img/Standard/img_cpfl/ic_hive.png", ":/img/Lite/img_cpfl/ic_hive2.png", "德迅蜂巢（容器安全）",
        "通过提供覆盖容器全生命周期的一站式容器安全解决方案，德迅蜂巢可实现容器安全预测、防御、检测和响应的安全闭环",
        CARD_ID_CPFL_HIVE_CONTAINER);
    AddYwaq(":/img/Standard/img_cpfl/ic_honeypot.png", ":/img/Lite/img_cpfl/ic_honeypot2.png", "蜜罐",
            "部署诱饵和陷阱在关键网络入口，诱导攻击者攻击伪装目标，保护真实资产，并且对攻击者做行为取证和追踪溯源，提升"
            "主动防御能力",
            CARD_ID_CPFL_HONEYPOT);
    AddYwaq(":/img/Standard/img_cpfl/ic_anti_dshield.png", ":/img/Lite/img_cpfl/ic_anti_dshield2.png",
            "德迅零域（微隔离）",
            "实现跨平台的统一安全管理，通过自主学习分析、可视化展示业务访问关系，实现细粒度、自适应的安全策略管理",
            CARD_ID_CPFL_ZERO_TRUST);

    AddYwaq(":/img/Standard/img_cpfl/ic_cloudatlas.png", ":/img/Lite/img_cpfl/ic_cloudatlas2.png",
            "德迅云图（威胁检测与分析）",
            "依赖云端强大的基础数据收集系统 ，快速且自动化的生产高覆盖度、高准确度、上下文丰富的情报数据",
            CARD_ID_CPFL_CLOUD_ATLAS);
}

void CpflManager::InitAqyy()
{
    AddAqyy(":/img/Standard/img_cpfl/ic_expert.png", ":/img/Lite/img_cpfl/ic_expert2.png", "专家服务",
            "专业安全专家和业务支撑团队，快速全面解决安全痛点，省市安保支撑单位，10年一线安服实战经验积累，服务千家政企"
            "客户，获得高度认可...",
            CARD_ID_CPFL_EXPERT_SERVICE);
    AddAqyy(
        ":/img/Standard/img_cpfl/ic_classifiedprotection.png", ":/img/Lite/img_cpfl/ic_classifiedprotection2.png",
        "等保合规咨询",
        "快速省心过等保，就选德迅云安全。德迅云安全为客户提供高性价比的等保合规安全一站式解决方案，满足等保合规要求",
        CARD_ID_CPFL_COMPLIANCE_CONSULTING);
    AddAqyy(":/img/Standard/img_cpfl/ic_situation.png", ":/img/Lite/img_cpfl/ic_situation2.png", "态势感知",
            "态势感知采用先进的大数据架构，通过采集系统的网络安全数据信息，对所有安全数据进行统一处理分析",
            CARD_ID_CPFL_SITUATION_AWARENESS);
    AddAqyy(":/img/Standard/img_cpfl/ic_audit.png", ":/img/Lite/img_cpfl/ic_audit2.png", "日记审计",
            "通过全面日志采集、实时关联分析、威胁事件告警及丰富多维审计，实现系统信息的统一集中存储与管理，关联与分析",
            CARD_ID_CPFL_LOG_AUDIT);
    AddAqyy(
        ":/img/Standard/img_cpfl/ic_database.png", ":/img/Lite/img_cpfl/ic_database2.png", "数据库审计",
        "为用户提供数据库审计与监控能力，对数据库风险操作行为进行实时记录与告警，提供数据库权限管控、事后行为审计能力",
        CARD_ID_CPFL_DATABASE_AUDIT);
}

void CpflManager::InitAqfw()
{
    AddAqfw(":/img/Standard/img_cpfl/ic_test.png", ":/img/Lite/img_cpfl/ic_test2.png", "渗透测试(模拟黑客入侵)",
            "模拟黑客攻击对业务系统进行安全性测试，比黑客更早发现可导致企业数据泄露、资产受损、数据被篡改等漏洞，并协助"
            "企业进行修复",
            CARD_ID_CPFL_PENETRATION_TEST);
    AddAqfw(":/img/Standard/img_cpfl/ic_code.png", ":/img/Lite/img_cpfl/ic_code2.png", "代码审计",
            "通过分析源代码，充分挖掘代码中存在的安全缺陷以及规范性缺陷。找到普通安全测试所无法发现的如二次注入、反序列"
            "化等安全漏洞",
            CARD_ID_CPFL_CODE_AUDIT);
    AddAqfw(":/img/Standard/img_cpfl/ic_risk.png", ":/img/Lite/img_cpfl/ic_risk2.png", "风险评估",
            "帮助企业分析资产所面临的威胁，及其存在的脆弱性，评估安全事件一旦发生可能造成的危害程度。并提出有针对性抵御"
            "威胁的防护对策",
            CARD_ID_CPFL_RISK_ASSESSMENT);
    AddAqfw(":/img/Standard/img_cpfl/ic_drill.png", ":/img/Lite/img_cpfl/ic_drill2.png", "信息安全对抗演习",
            "以攻促防，用实践来检验用户公司安全水平的高低，提升安全人员的安全技能和防护水平",
            CARD_ID_CPFL_SECURITY_DRILL);
    AddAqfw(":/img/Standard/img_cpfl/ic_emergency.png", ":/img/Lite/img_cpfl/ic_emergency2.png", "网络安全应急中心",
            "快速解决服务器被黑客控制、服务器被种后门、木马、病毒等严重的黑客入侵事件", CARD_ID_CPFL_EMERGENCY_CENTER);
}

void CpflManager::UpdateLayout(QList<CpflCardWgt *> &cards, QWidget *container, QWidget *scrollContainer)
{
    if (!container)
        return;

    // 防止递归调用
    if (is_updating_layout_)
    {
        return;
    }

    is_updating_layout_ = true;

    // Clear the existing layout and widgets
    if (QLayout *layout = container->layout())
    {
        QLayoutItem *item;
        while ((item = layout->takeAt(0)) != nullptr)
        {
            if (QWidget *widget = item->widget())
            {
                widget->hide();
                widget->setParent(nullptr);
            }
            delete item;
        }
        delete layout;
    }

    int col_cnt =
        qCeil(static_cast<double>(container->width() + 8 + container->contentsMargins().right()) / (249.0 + 8.0));
    if (col_cnt <= 0)
        col_cnt = 1;

    int row_cnt = cards.count() / col_cnt;
    if (cards.count() % col_cnt != 0)
    {
        row_cnt++;
    }

    // 动态调整滚动容器的高度
    if (scrollContainer)
    {
        int delta_row = row_cnt - 2;
        if (delta_row > 0)
        {
            scrollContainer->resize(516, 360 + delta_row * (152 + 8));
        }
    }

    QVBoxLayout *v_layout = new QVBoxLayout;
    v_layout->setSpacing(8);
    v_layout->setContentsMargins(0, 0, 0, 0);
    container->setLayout(v_layout);

    for (int row = 0; row < row_cnt; ++row)
    {
        QHBoxLayout *h_layout = new QHBoxLayout();
        v_layout->addLayout(h_layout);

        for (int col = 0; col < col_cnt; ++col)
        {
            int card_idx = row * col_cnt + col;
            if (card_idx > cards.count() - 1)
            {
                h_layout->addStretch(1);
            }
            else
            {
                // 设置正确的父容器
                cards[card_idx]->setParent(container);
                h_layout->addWidget(cards[card_idx], 1);
                cards[card_idx]->show();
            }
        }
    }

    // 重置标志位
    is_updating_layout_ = false;
}

void CpflManager::UpdateYaq(QWidget *container, QWidget *scrollContainer)
{
    UpdateLayout(yaq_cpfl_cards_, container, scrollContainer);
}

void CpflManager::UpdateYwaq(QWidget *container, QWidget *scrollContainer)
{
    UpdateLayout(ywaq_cpfl_cards_, container, scrollContainer);
}

void CpflManager::UpdateAqyy(QWidget *container, QWidget *scrollContainer)
{
    UpdateLayout(aqyy_cpfl_cards_, container, scrollContainer);
}

void CpflManager::UpdateAqfw(QWidget *container, QWidget *scrollContainer)
{
    UpdateLayout(aqfw_cpfl_cards_, container, scrollContainer);
}

void CpflManager::SetTheme(int type)
{
    current_theme_ = type;

    // 更新所有卡片的主题
    for (CpflCardWgt *card : yaq_cpfl_cards_)
    {
        card->SetTheme(type);
    }

    for (CpflCardWgt *card : ywaq_cpfl_cards_)
    {
        card->SetTheme(type);
    }

    for (CpflCardWgt *card : aqyy_cpfl_cards_)
    {
        card->SetTheme(type);
    }

    for (CpflCardWgt *card : aqfw_cpfl_cards_)
    {
        card->SetTheme(type);
    }
}

QList<CpflCardWgt *> CpflManager::GetCpflCards() const
{
    QList<CpflCardWgt *> all_cards;
    all_cards.append(yaq_cpfl_cards_);
    all_cards.append(ywaq_cpfl_cards_);
    all_cards.append(aqyy_cpfl_cards_);
    all_cards.append(aqfw_cpfl_cards_);
    return all_cards;
}

void CpflManager::OnCardDetailClicked()
{
    // 获取发送信号的卡片
    CpflCardWgt *card = qobject_cast<CpflCardWgt *>(sender());
    if (card)
    {
        HandleCardDetail(card->GetCardID());
    }
}

void CpflManager::HandleCardDetail(int cardID)
{
    switch (cardID)
    {
    // 云安全类别
    case CARD_ID_CPFL_DDOS_DEFENSE:
        ShowDdosDefenseDetail();
        break;
    case CARD_ID_CPFL_SECURE_CDN:
        ShowSecureCdnDetail();
        break;
    case CARD_ID_CPFL_ANTI_D_SHIELD:
        ShowAntiDShieldDetail();
        break;
    case CARD_ID_CPFL_SSL_CERTIFICATE:
        ShowSslCertificateDetail();
        break;
    case CARD_ID_CPFL_APP_ACCELERATION:
        ShowAppAccelerationDetail();
        break;
    case CARD_ID_CPFL_VULNERABILITY_SCAN:
        ShowVulnerabilityScanDetail();
        break;

    // 业务安全类别
    case CARD_ID_CPFL_GUARD:
        ShowGuardDetail();
        break;
    case CARD_ID_CPFL_CLOUD_MONITORING:
        ShowCloudMonitoringDetail();
        break;
    case CARD_ID_CPFL_HIVE_CONTAINER:
        ShowHiveContainerDetail();
        break;
    case CARD_ID_CPFL_HONEYPOT:
        ShowHoneypotDetail();
        break;
    case CARD_ID_CPFL_ZERO_TRUST:
        ShowZeroTrustDetail();
        break;
    case CARD_ID_CPFL_CLOUD_ATLAS:
        ShowCloudAtlasDetail();
        break;

    // 安全运营类别
    case CARD_ID_CPFL_EXPERT_SERVICE:
        ShowExpertServiceDetail();
        break;
    case CARD_ID_CPFL_COMPLIANCE_CONSULTING:
        ShowComplianceConsultingDetail();
        break;
    case CARD_ID_CPFL_SITUATION_AWARENESS:
        ShowSituationAwarenessDetail();
        break;
    case CARD_ID_CPFL_LOG_AUDIT:
        ShowLogAuditDetail();
        break;
    case CARD_ID_CPFL_DATABASE_AUDIT:
        ShowDatabaseAuditDetail();
        break;

    // 安全服务类别
    case CARD_ID_CPFL_PENETRATION_TEST:
        ShowPenetrationTestDetail();
        break;
    case CARD_ID_CPFL_CODE_AUDIT:
        ShowCodeAuditDetail();
        break;
    case CARD_ID_CPFL_RISK_ASSESSMENT:
        ShowRiskAssessmentDetail();
        break;
    case CARD_ID_CPFL_SECURITY_DRILL:
        ShowSecurityDrillDetail();
        break;
    case CARD_ID_CPFL_EMERGENCY_CENTER:
        ShowEmergencyCenterDetail();
        break;

    default:
        QMessageBox::information(nullptr, "提示", "未知的产品卡片，功能待实现");
        break;
    }
}

// 云安全详情函数
void CpflManager::ShowDdosDefenseDetail()
{
    QMessageBox::information(nullptr, "DDoS防御", "DDoS防御功能待实现");
}

void CpflManager::ShowSecureCdnDetail()
{
    QMessageBox::information(nullptr, "安全加速SCDN", "安全加速SCDN功能待实现");
}

void CpflManager::ShowAntiDShieldDetail()
{
    QMessageBox::information(nullptr, "抗D盾", "抗D盾功能待实现");
}

void CpflManager::ShowSslCertificateDetail()
{
    QMessageBox::information(nullptr, "SSL证书", "SSL证书功能待实现");
}

void CpflManager::ShowAppAccelerationDetail()
{
    QMessageBox::information(nullptr, "应用加速", "应用加速功能待实现");
}

void CpflManager::ShowVulnerabilityScanDetail()
{
    QMessageBox::information(nullptr, "漏洞扫描服务", "漏洞扫描服务功能待实现");
}

// 业务安全详情函数
void CpflManager::ShowGuardDetail()
{
    QMessageBox::information(nullptr, "德迅卫士", "德迅卫士功能待实现");
}

void CpflManager::ShowCloudMonitoringDetail()
{
    QMessageBox::information(nullptr, "云监测", "云监测功能待实现");
}

void CpflManager::ShowHiveContainerDetail()
{
    QMessageBox::information(nullptr, "德迅蜂巢", "德迅蜂巢功能待实现");
}

void CpflManager::ShowHoneypotDetail()
{
    QMessageBox::information(nullptr, "蜜罐", "蜜罐功能待实现");
}

void CpflManager::ShowZeroTrustDetail()
{
    QMessageBox::information(nullptr, "德迅零域", "德迅零域功能待实现");
}

void CpflManager::ShowCloudAtlasDetail()
{
    QMessageBox::information(nullptr, "德迅云图", "德迅云图功能待实现");
}

// 安全运营详情函数
void CpflManager::ShowExpertServiceDetail()
{
    QMessageBox::information(nullptr, "专家服务", "专家服务功能待实现");
}

void CpflManager::ShowComplianceConsultingDetail()
{
    QMessageBox::information(nullptr, "等保合规咨询", "等保合规咨询功能待实现");
}

void CpflManager::ShowSituationAwarenessDetail()
{
    QMessageBox::information(nullptr, "态势感知", "态势感知功能待实现");
}

void CpflManager::ShowLogAuditDetail()
{
    QMessageBox::information(nullptr, "日志审计", "日志审计功能待实现");
}

void CpflManager::ShowDatabaseAuditDetail()
{
    QMessageBox::information(nullptr, "数据库审计", "数据库审计功能待实现");
}

// 安全服务详情函数
void CpflManager::ShowPenetrationTestDetail()
{
    QMessageBox::information(nullptr, "渗透测试", "渗透测试功能待实现");
}

void CpflManager::ShowCodeAuditDetail()
{
    QMessageBox::information(nullptr, "代码审计", "代码审计功能待实现");
}

void CpflManager::ShowRiskAssessmentDetail()
{
    QMessageBox::information(nullptr, "风险评估", "风险评估功能待实现");
}

void CpflManager::ShowSecurityDrillDetail()
{
    QMessageBox::information(nullptr, "信息安全对抗演习", "信息安全对抗演习功能待实现");
}

void CpflManager::ShowEmergencyCenterDetail()
{
    QMessageBox::information(nullptr, "网络安全应急中心", "网络安全应急中心功能待实现");
}
