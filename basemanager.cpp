#include "basemanager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMessageBox>
#include <QProcess>
#include <Windows.h>
#include <shellapi.h>

BaseManager::BaseManager(QWidget *parent) : QObject(parent), parent_widget_(parent)
{
}

void BaseManager::ExecuteConfigFile(const QString &relativePath, const QString &description)
{

    QString appDir = QApplication::applicationDirPath();
    QString filePath = appDir + "/" + relativePath;

    qDebug() << "尝试执行文件:" << filePath;

    if (!QFile::exists(filePath))
    {
        qDebug() << "文件不存在:" << filePath;
        QMessageBox::warning(parent_widget_, "文件不存在",
                             QString("找不到文件: %1\n\n请检查配置文件是否正确释放。").arg(relativePath));
        return;
    }

    QString command;
    QString parameters;
    const wchar_t *verb = L"open";

    if (filePath.endsWith(".exe"))
    {
        command = "cmd.exe";
        parameters = QString("/c \"%1\"").arg(filePath);
    }
    else if (filePath.endsWith(".bat"))
    {
        command = "cmd.exe";
        parameters = QString("/c \"%1\"").arg(filePath);
    }
    else
    {
        command = filePath;
        parameters = "";
    }

    std::wstring wCommand = command.toStdWString();
    std::wstring wParameters = parameters.toStdWString();
    std::wstring wWorkingDir = appDir.toStdWString();

    HINSTANCE result = ShellExecuteW(NULL, verb, wCommand.c_str(), wParameters.c_str(), wWorkingDir.c_str(), SW_SHOW);

    if ((int)result <= 32)
    {
        QString errorMsg;
        switch ((int)result)
        {
        case ERROR_FILE_NOT_FOUND:
            errorMsg = "找不到指定的文件";
            break;
        case ERROR_PATH_NOT_FOUND:
            errorMsg = "找不到指定的路径";
            break;
        case ERROR_ACCESS_DENIED:
            errorMsg = "访问被拒绝，可能需要管理员权限";
            break;
        case ERROR_CANCELLED:
            errorMsg = "用户取消了UAC提示";
            break;
        default:
            errorMsg = QString("执行失败，错误代码: %1").arg((int)result);
            break;
        }

        qDebug() << "ShellExecute执行失败:" << errorMsg;
        QMessageBox::warning(parent_widget_, "执行失败",
                             QString("无法执行: %1\n错误信息: %2").arg(description).arg(errorMsg));
    }
    else
    {
        qDebug() << "ShellExecute执行成功:" << description;
    }
}
