<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ConfWgt</class>
 <widget class="QWidget" name="ConfWgt">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>467</width>
    <height>301</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background: transparent;</string>
  </property>
  <widget class="QWidget" name="wgt_confi_title" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>467</width>
     <height>36</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">QWidget
{
	border-top-left-radius:6px;
    border-top-right-radius:6px;
    border-bottom-left-radius:0px;
    border-bottom-right-radius:0px;
	border-image: url(:/img/conf_title_bg.png);
}</string>
   </property>
   <widget class="QPushButton" name="btn_close">
    <property name="geometry">
     <rect>
      <x>431</x>
      <y>0</y>
      <width>36</width>
      <height>36</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton
{
background: transparent;
border-image:none;
	border-top-left-radius:0px;
    border-top-right-radius:6px;
    border-bottom-left-radius:0px;
    border-bottom-right-radius:0px;
}
QPushButton:hover
{
background: #FF5150;
}</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset resource="res.qrc">
      <normaloff>:/img/close_hover.png</normaloff>:/img/close_hover.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>14</width>
      <height>14</height>
     </size>
    </property>
   </widget>
   <widget class="QLabel" name="label_logo">
    <property name="geometry">
     <rect>
      <x>11</x>
      <y>12</y>
      <width>14</width>
      <height>13</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">border-image: url(:/img/conf_title_logo.png);</string>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="QLabel" name="label_title">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>9</y>
      <width>54</width>
      <height>16</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
      <pointsize>-1</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">font-family: Microsoft YaHei;
font-size: 12px;
color: #FFFFFF;
background:transparent;
border-image:none;
</string>
    </property>
    <property name="text">
     <string>设置</string>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>36</y>
     <width>116</width>
     <height>265</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>116</width>
     <height>265</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">background: #F2F3F7;
border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:6px;
border-bottom-right-radius:0px;</string>
   </property>
   <widget class="QPushButton" name="btn_theme">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>16</y>
      <width>96</width>
      <height>32</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
      <pointsize>-1</pointsize>
      <weight>50</weight>
      <bold>false</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
}
QPushButton:checked,QPushButton:hover
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #0665FF;

background: #FFFFFF;
border-radius: 6px 6px 6px 6px;
}</string>
    </property>
    <property name="text">
     <string>主题外观</string>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
    <attribute name="buttonGroup">
     <string notr="true">buttonGroup_left</string>
    </attribute>
   </widget>
   <widget class="QPushButton" name="btn_about">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>48</y>
      <width>96</width>
      <height>32</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #67768E;
}
QPushButton:checked,QPushButton:hover
{
font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #0665FF;

background: #FFFFFF;
border-radius: 6px 6px 6px 6px;
}</string>
    </property>
    <property name="text">
     <string>关于我们</string>
    </property>
    <property name="checkable">
     <bool>true</bool>
    </property>
    <property name="checked">
     <bool>false</bool>
    </property>
    <attribute name="buttonGroup">
     <string notr="true">buttonGroup_left</string>
    </attribute>
   </widget>
   <widget class="QPushButton" name="btn_jc">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>220</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #7A869A;</string>
    </property>
    <property name="text">
     <string>查看教程</string>
    </property>
    <property name="icon">
     <iconset resource="res.qrc">
      <normaloff>:/img/ckjc.png</normaloff>:/img/ckjc.png</iconset>
    </property>
   </widget>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="geometry">
    <rect>
     <x>116</x>
     <y>36</y>
     <width>351</width>
     <height>265</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">border-top-left-radius:0px;
border-top-right-radius:0px;
border-bottom-left-radius:0px;
border-bottom-right-radius:6px;
background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <widget class="QWidget" name="page_theme">
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>16</x>
       <y>16</y>
       <width>48</width>
       <height>16</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #111111;</string>
     </property>
     <property name="text">
      <string>主题外观</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_confirm">
     <property name="geometry">
      <rect>
       <x>255</x>
       <y>211</y>
       <width>80</width>
       <height>32</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background: #0665FF;
border-radius: 4px;
font-family: Microsoft YaHei;
font-size: 12px;
color: #FFFFFF;</string>
     </property>
     <property name="text">
      <string>确定</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_4">
     <property name="geometry">
      <rect>
       <x>87</x>
       <y>152</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #3D4B64;</string>
     </property>
     <property name="text">
      <string>正常版</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_5">
     <property name="geometry">
      <rect>
       <x>248</x>
       <y>152</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei;
font-weight: 400;
font-size: 12px;
color: #3D4B64;</string>
     </property>
     <property name="text">
      <string>简约版</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_normal">
     <property name="geometry">
      <rect>
       <x>73</x>
       <y>154</y>
       <width>10</width>
       <height>10</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton
{
border: 1px solid #D0D0D0;
border-radius:5px;

}
QPushButton:checked
{
background: #0665FF;
border-radius:5px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <property name="checked">
      <bool>true</bool>
     </property>
     <attribute name="buttonGroup">
      <string notr="true">buttonGroup_skin</string>
     </attribute>
    </widget>
    <widget class="QPushButton" name="btn_simple">
     <property name="geometry">
      <rect>
       <x>234</x>
       <y>154</y>
       <width>10</width>
       <height>10</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton
{
border: 1px solid #D0D0D0;
border-radius:5px;

}
QPushButton:checked
{
background: #0665FF;
border-radius:5px;
}</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <attribute name="buttonGroup">
      <string notr="true">buttonGroup_skin</string>
     </attribute>
    </widget>
    <widget class="QLabel" name="pic_normal">
     <property name="geometry">
      <rect>
       <x>25</x>
       <y>48</y>
       <width>140</width>
       <height>96</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">border-image: url(:/img/theme_normal_checked.png);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="pic_simple">
     <property name="geometry">
      <rect>
       <x>186</x>
       <y>48</y>
       <width>140</width>
       <height>96</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">border-image: url(:/img/theme_simple.png);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="page_about">
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>156</x>
       <y>16</y>
       <width>40</width>
       <height>40</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">border-image: url(:/img/Icon_1.png);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>146</x>
       <y>64</y>
       <width>91</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-size: 12px;
color: #111111;</string>
     </property>
     <property name="text">
      <string>德迅工具箱</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_6">
     <property name="geometry">
      <rect>
       <x>157</x>
       <y>88</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 10px;
color: #3D4B64;</string>
     </property>
     <property name="text">
      <string>版本 1.2.1.0</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>60</x>
       <y>109</y>
       <width>251</width>
       <height>16</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 10px;
color: #3D4B64;</string>
     </property>
     <property name="text">
      <string>©浙江德迅网络安全技术有限公司。保留所有权利。</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_8">
     <property name="geometry">
      <rect>
       <x>24</x>
       <y>138</y>
       <width>303</width>
       <height>1</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background:#E5E5E5;
border-radius:0px;</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_9">
     <property name="geometry">
      <rect>
       <x>16</x>
       <y>155</y>
       <width>91</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">font-family: Microsoft YaHei, Microsoft YaHei;
font-size: 12px;
color: #111111;</string>
     </property>
     <property name="text">
      <string>免责声明</string>
     </property>
    </widget>
    <widget class="QPlainTextEdit" name="plainTextEdit">
     <property name="geometry">
      <rect>
       <x>32</x>
       <y>170</y>
       <width>295</width>
       <height>81</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPlainTextEdit
{
font-family: Microsoft YaHei, Microsoft YaHei;
font-weight: 400;
font-size: 10px;
color: #67768E;
line-height: 100%;
background:transparent;
}


QScrollBar:vertical {/*设置滚动条背景*/
    border: none;
	border-radius:2px;
    width: 4px;
	background:transparent;
}
QScrollBar::handle:vertical {/*设置滑动条*/
	border: none;
    border-radius:2px; 
	background-color: #BDBDBD;
  }
QScrollBar::sub-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: top;
      subcontrol-origin: margin;
  }
QScrollBar::add-line:vertical {
      border: none;
      height: 0px;
      subcontrol-position: bottom;
      subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
	border:none;
      width: 0px;
      height: 0px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
      background: none;
  }</string>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAlwaysOff</enum>
     </property>
     <property name="plainText">
      <string>本软件及相关文档仅供参考，不保证其完整性、准确性和适用性。用户在使用本软件过程中所产生的任何损失或责任，与本公司无关。本公司不承担因使用或无法使用本软件所导致的任何直接、间接、附带、特殊或后续性损害责任。建议您在使用前备份重要数据，并确保系统环境符合运行要求。如有疑问，请联系浙江德迅网络安全技术有限公司客服中心。</string>
     </property>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources>
  <include location="res.qrc"/>
 </resources>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup_left"/>
  <buttongroup name="buttonGroup_skin"/>
 </buttongroups>
</ui>
