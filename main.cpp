#include "dxwswgt.h"
#include <DbgHelp.h>
#include <Psapi.h>
#include <QApplication>
#include <QDebug>
#include <QIcon>
#include <QMessageBox>
#include <QSharedMemory>
#include <QSystemSemaphore>

#ifdef _WIN32
#include <Windows.h>
#include <fcntl.h>
#include <io.h>
#endif

#include <crtdbg.h>
#include <signal.h>
#include <tchar.h>
#pragma comment(lib, "dbghelp.lib")
#pragma comment(lib, "psapi.lib")

#ifdef ENABLE_DEBUG_WINDOW
#include "debugwindow.h"
#include "spdlog/sinks/msvc_sink.h"
#include "spdlog/sinks/qt_sinks.h"
#include "spdlog/spdlog.h"
#endif

// 前向声明全局崩溃处理函数
extern void SetupGlobalCrashHandler();

int main(int argc, char *argv[])
{
// 设置控制台代码页为UTF-8，解决中文乱码
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    qSetMessagePattern("%{file}:%{line} - %{message}");
    // 0. 首先设置全局崩溃处理器
    SetupGlobalCrashHandler();

    // 1. 立即创建QApplication实例
    QApplication a(argc, argv);

#ifdef ENABLE_DEBUG_WINDOW
    DebugWindow *debugWindow = new DebugWindow();
    // 注意：qt_color_sink_mt 第4个参数 is_utf8 默认为 false，需要设置为 true 以支持 UTF-8 编码，否则导致中文乱码。
    auto qt_sink = std::make_shared<spdlog::sinks::qt_color_sink_mt>(debugWindow->getLogWidget(), 500, true, true);
    auto msvc_sink = std::make_shared<spdlog::sinks::msvc_sink_mt>();

    std::vector<spdlog::sink_ptr> sinks;
    sinks.push_back(qt_sink);
    sinks.push_back(msvc_sink);

    auto logger = std::make_shared<spdlog::logger>("app", begin(sinks), end(sinks));
    spdlog::set_default_logger(logger);

    spdlog::set_level(spdlog::level::debug); // Set global log level to debug
    spdlog::flush_on(spdlog::level::debug);

    debugWindow->show();

#endif

    // 2. 将单例检测逻辑放在 QApplication 创建之后
    QSystemSemaphore semaphore("DxwsWgtAppSemaphore", 1);
    semaphore.acquire();

    QSharedMemory sharedMemory("DxwsWgtAppSharedMemory");
    bool isRunning = false;
    if (sharedMemory.attach())
    {
        isRunning = true;
    }
    else
    {
        if (!sharedMemory.create(1))
        {
            // 如果创建失败，可能存在权限问题或键冲突
            SPDLOG_ERROR("Unable to create shared memory segment.");
            isRunning = true;
        }
    }

    semaphore.release();

    if (isRunning)
    {
        // 现在调用 QMessageBox 是安全的，因为 QApplication 实例 'a' 已经存在
        QMessageBox::warning(nullptr, "提示", "应用程序已经在运行中。");
        return 0; // 正常退出
    }

    // 3. 继续进行应用程序的其余设置
    a.setQuitOnLastWindowClosed(false);
    QApplication::setStyle("Fusion");

    a.setWindowIcon(QIcon(":/WindowsAdapter.ico"));

    // 4. 创建并显示主窗口
    DxwsWgt w;
    w.show();

    return a.exec();
}
