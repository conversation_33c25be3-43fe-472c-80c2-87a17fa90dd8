#ifndef CARDWGT_H
#define CARDWGT_H

#include <QWidget>

namespace Ui
{
class CardWgt;
}

class CardWgt : public QWidget
{
    Q_OBJECT

  public:
    explicit CardWgt(QWidget *parent = nullptr);
    ~CardWgt();

    void setNormal_icon_path(const QString &normal_icon_path);

    void setSimple_icon_path(const QString &simple_icon_path);

    void SetTheme(int type);

    void SetName(QString name);

    QString GetName();

    QString GetNormalIconPath();

    QString GetSimpleIconPath();

    void SetCardId(int id);

    int GetCardId() const;

    int GetType() const
    {
        return type_;
    }

    void SetCollected(bool collected);

  signals:
    void cardClicked(int cardId);
    void collectClicked(int cardId, bool isCollected);

  protected:
    bool eventFilter(QObject *watched, QEvent *event) override;

  private slots:
    void on_btn_collect_clicked();

  private:
    void SetIcon(QString path);

    void SetIconGray(QString path);

    QString GetCurrentIconPath() const;

    QPixmap ConvertToGrayscale(const QPixmap &pixmap);

  private:
    Ui::CardWgt *ui;

    QString normal_icon_path_;
    QString simple_icon_path_;

    int type_ = 0;
    int card_id_ = -1; // 卡片唯一ID
};

#endif // CARDWGT_H
